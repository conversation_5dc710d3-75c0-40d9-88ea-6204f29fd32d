import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup.js'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        'dist/',
        '**/*.config.js',
        '**/*.config.mjs'
      ]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../src'),
      '@utils': resolve(__dirname, '../src/utils'),
      '@components': resolve(__dirname, '../src/components')
    }
  }
});
