// Test setup file for Vitest
import { vi } from 'vitest';

// Mock global objects that might not be available in test environment
global.performance = {
  now: vi.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000
  }
};

global.navigator = {
  hardwareConcurrency: 4,
  userAgent: 'test-agent'
};

// Mock fetch for API tests
global.fetch = vi.fn();

// Mock Leaflet for map tests
global.L = {
  map: vi.fn(() => ({
    setView: vi.fn(),
    addTo: vi.fn(),
    removeLayer: vi.fn(),
    fitBounds: vi.fn(),
    getBounds: vi.fn(() => ({
      getNorth: () => 75,
      getSouth: () => 60,
      getEast: () => -90,
      getWest: () => -110
    })),
    getZoom: vi.fn(() => 5),
    invalidateSize: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    remove: vi.fn()
  })),
  tileLayer: vi.fn(() => ({
    addTo: vi.fn(),
    remove: vi.fn()
  })),
  circle: vi.fn(() => ({
    addTo: vi.fn(),
    bindTooltip: vi.fn(),
    setStyle: vi.fn(),
    getLatLng: vi.fn(() => ({ lat: 65, lng: -100 })),
    setRadius: vi.fn()
  })),
  layerGroup: vi.fn(() => ({
    addTo: vi.fn(),
    removeLayer: vi.fn(),
    clearLayers: vi.fn(),
    eachLayer: vi.fn(),
    getLayers: vi.fn(() => [])
  })),
  featureGroup: vi.fn(() => ({
    getBounds: vi.fn(() => ({
      getNorth: () => 75,
      getSouth: () => 60,
      getEast: () => -90,
      getWest: () => -110
    }))
  })),
  marker: vi.fn(() => ({
    addTo: vi.fn(),
    bindTooltip: vi.fn()
  }))
};

// Mock DOM methods
global.document = {
  createElement: vi.fn(() => ({
    id: '',
    className: '',
    innerHTML: '',
    style: {},
    appendChild: vi.fn(),
    remove: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    classList: {
      add: vi.fn(),
      remove: vi.fn(),
      contains: vi.fn()
    }
  })),
  getElementById: vi.fn(() => ({
    style: {},
    textContent: '',
    appendChild: vi.fn(),
    addEventListener: vi.fn(),
    classList: {
      add: vi.fn(),
      remove: vi.fn()
    }
  })),
  body: {
    appendChild: vi.fn(),
    style: {}
  },
  addEventListener: vi.fn(),
  fullscreenElement: null,
  exitFullscreen: vi.fn(() => Promise.resolve())
};

global.window = {
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  location: {
    reload: vi.fn()
  },
  requestFullscreen: vi.fn(() => Promise.resolve())
};

// Mock import.meta.env
global.import = {
  meta: {
    env: {
      DEV: true
    }
  }
};

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
};

// Mock setTimeout and clearTimeout
global.setTimeout = vi.fn((fn, delay) => {
  if (typeof fn === 'function') {
    fn();
  }
  return 1;
});

global.clearTimeout = vi.fn();
global.setInterval = vi.fn(() => 1);
global.clearInterval = vi.fn();

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
  
  // Reset fetch mock
  global.fetch.mockReset();
  
  // Reset console mocks
  console.log.mockClear();
  console.warn.mockClear();
  console.error.mockClear();
});
