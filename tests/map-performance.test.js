// Unit tests for map performance utilities
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  DataOptimizer,
  MemoryManager,
  layerCache,
  performanceMonitor,
  memoryManager
} from '../src/utils/mapPerformance.js';

describe('Map Performance Utilities', () => {
  describe('DataOptimizer', () => {
    const mockAuroraPoints = [
      { latitude: 65, longitude: -100, intensity: 80 },
      { latitude: 66, longitude: -101, intensity: 75 },
      { latitude: 67, longitude: -102, intensity: 70 },
      { latitude: 68, longitude: -103, intensity: 65 },
      { latitude: 69, longitude: -104, intensity: 60 },
      { latitude: 70, longitude: -105, intensity: 55 },
      { latitude: 71, longitude: -106, intensity: 50 },
      { latitude: 72, longitude: -107, intensity: 45 },
      { latitude: 73, longitude: -108, intensity: 40 },
      { latitude: 74, longitude: -109, intensity: 35 }
    ];

    const mockBounds = {
      north: 75,
      south: 60,
      east: -95,
      west: -115
    };

    it('should filter points within viewport bounds', () => {
      const optimized = DataOptimizer.optimizePoints(mockAuroraPoints, 5, mockBounds, 100);
      
      // All mock points should be within bounds
      expect(optimized.length).toBe(mockAuroraPoints.length);
      
      optimized.forEach(point => {
        expect(point.latitude).toBeGreaterThanOrEqual(mockBounds.south);
        expect(point.latitude).toBeLessThanOrEqual(mockBounds.north);
        expect(point.longitude).toBeGreaterThanOrEqual(mockBounds.west);
        expect(point.longitude).toBeLessThanOrEqual(mockBounds.east);
      });
    });

    it('should limit points to maximum count', () => {
      const maxPoints = 5;
      const optimized = DataOptimizer.optimizePoints(mockAuroraPoints, 5, mockBounds, maxPoints);
      
      expect(optimized.length).toBeLessThanOrEqual(maxPoints);
    });

    it('should prioritize high-intensity points', () => {
      const maxPoints = 3;
      const optimized = DataOptimizer.optimizePoints(mockAuroraPoints, 5, mockBounds, maxPoints);
      
      // Should include highest intensity points
      const intensities = optimized.map(p => p.intensity || p.clusterSize);
      const sortedOriginal = mockAuroraPoints
        .sort((a, b) => b.intensity - a.intensity)
        .slice(0, maxPoints)
        .map(p => p.intensity);
      
      // Check that we got high-intensity points (allowing for clustering)
      expect(Math.max(...intensities)).toBeGreaterThanOrEqual(70);
    });

    it('should apply different clustering strategies based on zoom level', () => {
      const lowZoomResult = DataOptimizer.optimizePoints(mockAuroraPoints, 2, mockBounds, 100);
      const highZoomResult = DataOptimizer.optimizePoints(mockAuroraPoints, 7, mockBounds, 100);
      
      // Low zoom should have more aggressive clustering (fewer points)
      expect(lowZoomResult.length).toBeLessThanOrEqual(highZoomResult.length);
    });

    it('should handle empty input gracefully', () => {
      const result = DataOptimizer.optimizePoints([], 5, mockBounds, 100);
      expect(result).toEqual([]);
    });

    it('should handle null/undefined input gracefully', () => {
      const result1 = DataOptimizer.optimizePoints(null, 5, mockBounds, 100);
      const result2 = DataOptimizer.optimizePoints(undefined, 5, mockBounds, 100);
      
      expect(result1).toEqual([]);
      expect(result2).toEqual([]);
    });
  });

  describe('DataOptimizer.clusterPoints', () => {
    const closePoints = [
      { latitude: 65.0, longitude: -100.0, intensity: 50 },
      { latitude: 65.1, longitude: -100.1, intensity: 55 },
      { latitude: 65.2, longitude: -100.2, intensity: 60 },
      { latitude: 70.0, longitude: -110.0, intensity: 40 }
    ];

    it('should cluster nearby points', () => {
      const clustered = DataOptimizer.clusterPoints(closePoints, 1.0);
      
      // Should have fewer points than original due to clustering
      expect(clustered.length).toBeLessThan(closePoints.length);
    });

    it('should create representative cluster points', () => {
      const clustered = DataOptimizer.clusterPoints(closePoints, 1.0);
      
      const clusterPoint = clustered.find(p => p.isCluster);
      if (clusterPoint) {
        expect(clusterPoint).toHaveProperty('clusterSize');
        expect(clusterPoint.clusterSize).toBeGreaterThan(1);
        expect(clusterPoint.intensity).toBeGreaterThan(0);
      }
    });

    it('should not cluster distant points', () => {
      const distantPoints = [
        { latitude: 65, longitude: -100, intensity: 50 },
        { latitude: 75, longitude: -110, intensity: 55 }
      ];
      
      const clustered = DataOptimizer.clusterPoints(distantPoints, 1.0);
      expect(clustered.length).toBe(distantPoints.length);
    });
  });

  describe('LayerCache', () => {
    let cache;

    beforeEach(() => {
      cache = layerCache;
      cache.clear();
    });

    it('should store and retrieve cached layers', () => {
      const mockLayer = { id: 'test-layer', data: 'test-data' };
      const metadata = { pointCount: 10 };
      
      cache.set('test-key', mockLayer, metadata);
      const retrieved = cache.get('test-key');
      
      expect(retrieved).toBeTruthy();
      expect(retrieved.layer).toBe(mockLayer);
      expect(retrieved.metadata.pointCount).toBe(10);
    });

    it('should return null for non-existent keys', () => {
      const result = cache.get('non-existent-key');
      expect(result).toBeNull();
    });

    it('should implement LRU eviction', () => {
      // Fill cache beyond capacity
      for (let i = 0; i < 6; i++) {
        cache.set(`key-${i}`, { id: i }, {});
      }
      
      // First key should be evicted
      expect(cache.get('key-0')).toBeNull();
      expect(cache.get('key-5')).toBeTruthy();
    });

    it('should update access order on get', () => {
      cache.set('key-1', { id: 1 }, {});
      cache.set('key-2', { id: 2 }, {});
      
      // Access key-1 to make it most recently used
      cache.get('key-1');
      
      // Fill cache to trigger eviction
      for (let i = 3; i < 8; i++) {
        cache.set(`key-${i}`, { id: i }, {});
      }
      
      // key-1 should still exist (was accessed recently)
      expect(cache.get('key-1')).toBeTruthy();
      // key-2 should be evicted (least recently used)
      expect(cache.get('key-2')).toBeNull();
    });

    it('should provide cache statistics', () => {
      cache.set('key-1', { id: 1 }, {});
      cache.set('key-2', { id: 2 }, {});
      
      const stats = cache.getStats();
      
      expect(stats.size).toBe(2);
      expect(stats.maxSize).toBeGreaterThan(0);
      expect(stats.keys).toContain('key-1');
      expect(stats.keys).toContain('key-2');
    });
  });

  describe('PerformanceMonitor', () => {
    beforeEach(() => {
      // Clear any existing metrics
      performanceMonitor.metrics = {
        layerRenderTime: [],
        dataFetchTime: [],
        tileLoadTime: [],
        memoryUsage: []
      };
    });

    it('should track timing operations', () => {
      performanceMonitor.startTiming('test-operation');
      
      // Simulate some work
      const start = Date.now();
      while (Date.now() - start < 10) {
        // Wait 10ms
      }
      
      const duration = performanceMonitor.endTiming('test-operation');
      
      expect(duration).toBeGreaterThan(0);
      expect(duration).toBeLessThan(100); // Should be reasonable
    });

    it('should store metrics correctly', () => {
      performanceMonitor.addMetric('layerRenderTime', 150);
      performanceMonitor.addMetric('layerRenderTime', 200);
      
      const stats = performanceMonitor.getStats();
      
      expect(stats.layerRenderTime.count).toBe(2);
      expect(stats.layerRenderTime.avg).toBe(175);
      expect(stats.layerRenderTime.min).toBe(150);
      expect(stats.layerRenderTime.max).toBe(200);
    });

    it('should limit stored measurements', () => {
      // Add more than 50 measurements
      for (let i = 0; i < 60; i++) {
        performanceMonitor.addMetric('layerRenderTime', i);
      }
      
      const stats = performanceMonitor.getStats();
      expect(stats.layerRenderTime.count).toBe(50); // Should be limited to 50
    });

    it('should detect performance issues', () => {
      // Add slow measurements
      performanceMonitor.addMetric('layerRenderTime', 2000); // Slow rendering
      performanceMonitor.addMetric('dataFetchTime', 8000);   // Slow data fetch
      
      const status = performanceMonitor.checkPerformance();
      
      expect(status.isHealthy).toBe(false);
      expect(status.issues).toContain('Slow layer rendering');
      expect(status.issues).toContain('Slow data fetching');
    });

    it('should report healthy performance for good metrics', () => {
      performanceMonitor.addMetric('layerRenderTime', 100);
      performanceMonitor.addMetric('dataFetchTime', 1000);
      
      const status = performanceMonitor.checkPerformance();
      
      expect(status.isHealthy).toBe(true);
      expect(status.issues).toHaveLength(0);
    });
  });

  describe('MemoryManager', () => {
    let manager;

    beforeEach(() => {
      manager = new MemoryManager();
    });

    it('should track objects for cleanup', () => {
      const testObj = { id: 'test' };
      const cleanupFn = vi.fn();
      
      manager.track(testObj, cleanupFn);
      
      expect(manager.cleanupCallbacks).toHaveLength(1);
    });

    it('should execute cleanup functions', () => {
      const testObj = { id: 'test' };
      const cleanupFn = vi.fn();
      
      manager.track(testObj, cleanupFn);
      manager.cleanup();
      
      expect(cleanupFn).toHaveBeenCalledWith(testObj);
      expect(manager.cleanupCallbacks).toHaveLength(0);
    });

    it('should handle cleanup errors gracefully', () => {
      const testObj = { id: 'test' };
      const errorCleanupFn = vi.fn(() => {
        throw new Error('Cleanup failed');
      });
      const normalCleanupFn = vi.fn();
      
      manager.track(testObj, errorCleanupFn);
      manager.track({}, normalCleanupFn);
      
      // Should not throw and should continue with other cleanups
      expect(() => manager.cleanup()).not.toThrow();
      expect(normalCleanupFn).toHaveBeenCalled();
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete performance monitoring workflow', () => {
      // Start timing
      performanceMonitor.startTiming('integration-test');
      
      // Simulate data optimization
      const mockPoints = Array.from({ length: 100 }, (_, i) => ({
        latitude: 65 + i * 0.1,
        longitude: -100 - i * 0.1,
        intensity: Math.random() * 100
      }));
      
      const bounds = { north: 75, south: 60, east: -90, west: -110 };
      const optimized = DataOptimizer.optimizePoints(mockPoints, 5, bounds, 50);
      
      // End timing
      const duration = performanceMonitor.endTiming('integration-test');
      
      // Verify results
      expect(optimized.length).toBeLessThanOrEqual(50);
      expect(duration).toBeGreaterThan(0);
      
      const stats = performanceMonitor.getStats();
      expect(stats['integration-test'].count).toBe(1);
    });
  });
});
