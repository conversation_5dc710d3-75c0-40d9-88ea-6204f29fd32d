// Unit tests for aurora data processing utilities
import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  convertLongitude360To180,
  isValidAuroraPoint,
  getAuroraColor,
  calculateAuroraRadius,
  processOvationData,
  validateAuroraData,
  getFallbackAuroraData
} from '../src/utils/auroraData.js';

describe('Aurora Data Processing', () => {
  describe('convertLongitude360To180', () => {
    it('should convert 360-degree longitude to 180-degree system', () => {
      expect(convertLongitude360To180(0)).toBe(0);
      expect(convertLongitude360To180(180)).toBe(180);
      expect(convertLongitude360To180(270)).toBe(-90);
      expect(convertLongitude360To180(360)).toBe(0);
      expect(convertLongitude360To180(45)).toBe(45);
      expect(convertLongitude360To180(225)).toBe(-135);
    });
  });

  describe('isValidAuroraPoint', () => {
    it('should validate correct aurora data points', () => {
      expect(isValidAuroraPoint([0, 0, 50])).toBe(true);
      expect(isValidAuroraPoint([-180, -90, 0])).toBe(true);
      expect(isValidAuroraPoint([360, 90, 100])).toBe(true);
    });

    it('should reject invalid aurora data points', () => {
      expect(isValidAuroraPoint([])).toBe(false);
      expect(isValidAuroraPoint([0, 0])).toBe(false);
      expect(isValidAuroraPoint([0, 0, 50, 100])).toBe(false);
      expect(isValidAuroraPoint(['invalid', 0, 50])).toBe(false);
      expect(isValidAuroraPoint([0, 100, 50])).toBe(false); // Invalid latitude
      expect(isValidAuroraPoint([400, 0, 50])).toBe(false); // Invalid longitude
      expect(isValidAuroraPoint([0, 0, -10])).toBe(false); // Negative intensity
    });
  });

  describe('getAuroraColor', () => {
    it('should return correct colors for northern hemisphere', () => {
      const veryHighColor = getAuroraColor(90, 'northern');
      const highColor = getAuroraColor(70, 'northern');
      const moderateColor = getAuroraColor(50, 'northern');
      const lowColor = getAuroraColor(30, 'northern');
      const veryLowColor = getAuroraColor(10, 'northern');
      const transparentColor = getAuroraColor(2, 'northern');

      expect(veryHighColor).toContain('rgba(255, 0, 255');
      expect(highColor).toContain('rgba(255, 100, 100');
      expect(moderateColor).toContain('rgba(255, 255, 0');
      expect(lowColor).toContain('rgba(0, 255, 100');
      expect(veryLowColor).toContain('rgba(0, 150, 255');
      expect(transparentColor).toBe('rgba(0, 0, 0, 0)');
    });

    it('should return different colors for southern hemisphere', () => {
      const northernColor = getAuroraColor(50, 'northern');
      const southernColor = getAuroraColor(50, 'southern');
      
      expect(northernColor).not.toBe(southernColor);
      expect(southernColor).toContain('rgba(255, 200, 0');
    });
  });

  describe('calculateAuroraRadius', () => {
    it('should calculate radius based on intensity', () => {
      const baseRadius = 50000;
      
      const lowRadius = calculateAuroraRadius(10, baseRadius);
      const highRadius = calculateAuroraRadius(90, baseRadius);
      
      expect(lowRadius).toBeGreaterThan(baseRadius * 0.3);
      expect(lowRadius).toBeLessThan(baseRadius);
      expect(highRadius).toBeGreaterThan(baseRadius);
      expect(highRadius).toBeLessThan(baseRadius * 2.5);
    });

    it('should respect minimum and maximum bounds', () => {
      const baseRadius = 50000;
      const minRadius = baseRadius * 0.3;
      const maxRadius = baseRadius * 2.5;
      
      const zeroRadius = calculateAuroraRadius(0, baseRadius);
      const maxIntensityRadius = calculateAuroraRadius(100, baseRadius);
      
      expect(zeroRadius).toBeGreaterThanOrEqual(minRadius);
      expect(maxIntensityRadius).toBeLessThanOrEqual(maxRadius);
    });
  });

  describe('processOvationData', () => {
    const mockRawData = {
      coordinates: [
        [0, 65, 45],      // Northern hemisphere
        [180, -65, 30],   // Southern hemisphere
        [270, 70, 80],    // High intensity northern
        [90, -70, 0.5],   // Very low intensity (should be filtered)
        ['invalid', 0, 50] // Invalid data (should be filtered)
      ],
      'Observation Time': '2023-12-01T12:00:00Z'
    };

    it('should process valid OVATION data correctly', () => {
      const result = processOvationData(mockRawData);
      
      expect(result).toHaveProperty('northern');
      expect(result).toHaveProperty('southern');
      expect(result).toHaveProperty('metadata');
      
      expect(result.northern).toHaveLength(2); // Two valid northern points
      expect(result.southern).toHaveLength(1); // One valid southern point
      
      expect(result.metadata.observationTime).toBe('2023-12-01T12:00:00Z');
      expect(result.metadata.totalPoints).toBe(5);
      expect(result.metadata.processedPoints).toBe(3);
    });

    it('should handle invalid data gracefully', () => {
      const invalidData = { invalid: 'data' };
      const result = processOvationData(invalidData);
      
      expect(result.northern).toHaveLength(0);
      expect(result.southern).toHaveLength(0);
      expect(result.metadata).toBe(null);
    });

    it('should filter out very low intensity points', () => {
      const lowIntensityData = {
        coordinates: [
          [0, 65, 0.5],  // Below threshold
          [0, 66, 1.5]   // Above threshold
        ]
      };
      
      const result = processOvationData(lowIntensityData);
      expect(result.northern).toHaveLength(1);
    });

    it('should prevent duplicate points', () => {
      const duplicateData = {
        coordinates: [
          [0, 65, 45],
          [0.01, 65.01, 50], // Very close to first point
          [0, 65, 55]        // Exact duplicate
        ]
      };
      
      const result = processOvationData(duplicateData);
      expect(result.northern).toHaveLength(1); // Should deduplicate
    });
  });

  describe('validateAuroraData', () => {
    it('should validate correct aurora data structure', () => {
      const validData = {
        northern: [{ latitude: 65, longitude: 0, intensity: 45 }],
        southern: [{ latitude: -65, longitude: 180, intensity: 30 }],
        metadata: { observationTime: '2023-12-01T12:00:00Z' }
      };
      
      expect(validateAuroraData(validData)).toBe(true);
    });

    it('should reject invalid aurora data structure', () => {
      expect(validateAuroraData(null)).toBe(false);
      expect(validateAuroraData({})).toBe(false);
      expect(validateAuroraData({ northern: 'invalid' })).toBe(false);
      expect(validateAuroraData({ 
        northern: [], 
        southern: [], 
        metadata: null 
      })).toBe(false);
    });
  });

  describe('getFallbackAuroraData', () => {
    it('should return fallback data for both hemispheres', () => {
      const fallbackData = getFallbackAuroraData();
      
      expect(fallbackData.northern).toHaveLength(4);
      expect(fallbackData.southern).toHaveLength(3);
      expect(fallbackData.metadata.isFallback).toBe(true);
    });

    it('should return only northern hemisphere data when requested', () => {
      const northernData = getFallbackAuroraData('northern');
      
      expect(northernData.northern).toHaveLength(4);
      expect(northernData.southern).toHaveLength(0);
    });

    it('should return only southern hemisphere data when requested', () => {
      const southernData = getFallbackAuroraData('southern');
      
      expect(southernData.northern).toHaveLength(0);
      expect(southernData.southern).toHaveLength(3);
    });

    it('should include proper metadata', () => {
      const fallbackData = getFallbackAuroraData();
      
      expect(fallbackData.metadata).toHaveProperty('observationTime');
      expect(fallbackData.metadata).toHaveProperty('totalPoints');
      expect(fallbackData.metadata).toHaveProperty('processedPoints');
      expect(fallbackData.metadata).toHaveProperty('northernPoints');
      expect(fallbackData.metadata).toHaveProperty('southernPoints');
      expect(fallbackData.metadata.isFallback).toBe(true);
    });
  });
});

describe('Aurora Data Integration', () => {
  beforeEach(() => {
    // Reset any global state
    vi.clearAllMocks();
  });

  it('should handle complete data processing pipeline', () => {
    const rawOvationData = {
      coordinates: [
        [45, 65, 75],    // High intensity northern
        [225, -65, 60],  // High intensity southern
        [135, 70, 25],   // Moderate intensity northern
        [315, -70, 15]   // Low intensity southern
      ],
      'Observation Time': '2023-12-01T15:30:00Z'
    };

    const processedData = processOvationData(rawOvationData);
    
    // Validate the processed data
    expect(validateAuroraData(processedData)).toBe(true);
    
    // Check that all points have required properties
    [...processedData.northern, ...processedData.southern].forEach(point => {
      expect(point).toHaveProperty('latitude');
      expect(point).toHaveProperty('longitude');
      expect(point).toHaveProperty('intensity');
      expect(point).toHaveProperty('color');
      expect(point).toHaveProperty('radius');
      
      // Validate longitude conversion
      expect(point.longitude).toBeGreaterThanOrEqual(-180);
      expect(point.longitude).toBeLessThanOrEqual(180);
    });
    
    // Check metadata completeness
    expect(processedData.metadata.northernPoints).toBe(processedData.northern.length);
    expect(processedData.metadata.southernPoints).toBe(processedData.southern.length);
    expect(processedData.metadata.processedPoints).toBe(
      processedData.northern.length + processedData.southern.length
    );
  });
});
