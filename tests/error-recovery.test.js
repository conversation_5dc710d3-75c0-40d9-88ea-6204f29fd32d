// Unit tests for error recovery utilities
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  RetryManager,
  ErrorHandler,
  DegradationManager,
  NotificationManager,
  retryManager,
  errorHandler,
  degradationManager,
  notificationManager
} from '../src/utils/errorRecovery.js';

// Mock DOM methods for testing
global.document = {
  createElement: vi.fn(() => ({
    id: '',
    className: '',
    innerHTML: '',
    style: {},
    appendChild: vi.fn(),
    remove: vi.fn()
  })),
  getElementById: vi.fn(() => null),
  body: {
    appendChild: vi.fn()
  }
};

global.setTimeout = vi.fn((fn, delay) => {
  fn();
  return 1;
});

global.clearTimeout = vi.fn();

describe('Error Recovery System', () => {
  describe('RetryManager', () => {
    let manager;

    beforeEach(() => {
      manager = new RetryManager(3, 1000, 10000);
      vi.clearAllTimers();
    });

    it('should execute function successfully on first try', async () => {
      const mockFn = vi.fn().mockResolvedValue('success');
      
      const result = await manager.executeWithRetry('test-key', mockFn);
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const mockFn = vi.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue('success');
      
      const result = await manager.executeWithRetry('test-key', mockFn);
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retries', async () => {
      const mockFn = vi.fn().mockRejectedValue(new Error('Persistent failure'));
      
      await expect(
        manager.executeWithRetry('test-key', mockFn)
      ).rejects.toThrow('Persistent failure');
      
      expect(mockFn).toHaveBeenCalledTimes(4); // Initial + 3 retries
    });

    it('should respect shouldRetry option', async () => {
      const mockFn = vi.fn().mockRejectedValue(new Error('Network error'));
      
      await expect(
        manager.executeWithRetry('test-key', mockFn, {
          shouldRetry: (error) => !error.message.includes('Network')
        })
      ).rejects.toThrow('Network error');
      
      expect(mockFn).toHaveBeenCalledTimes(1); // No retries
    });

    it('should reset retry count on success', async () => {
      const mockFn = vi.fn()
        .mockRejectedValueOnce(new Error('Failure'))
        .mockResolvedValue('success');
      
      // First execution with retry
      await manager.executeWithRetry('test-key', mockFn);
      
      // Second execution should start fresh
      const mockFn2 = vi.fn().mockResolvedValue('success2');
      const result = await manager.executeWithRetry('test-key', mockFn2);
      
      expect(result).toBe('success2');
      expect(manager.retryAttempts.has('test-key')).toBe(false);
    });

    it('should provide retry statistics', () => {
      manager.retryAttempts.set('key1', 2);
      manager.retryAttempts.set('key2', 1);
      
      const stats = manager.getStats();
      
      expect(stats.activeRetries).toBe(2);
      expect(stats.retryAttempts.key1).toBe(2);
      expect(stats.retryAttempts.key2).toBe(1);
    });
  });

  describe('ErrorHandler', () => {
    let handler;

    beforeEach(() => {
      handler = new ErrorHandler();
    });

    it('should classify network errors correctly', () => {
      const networkError = new Error('Network timeout occurred');
      const type = handler.classifyError(networkError);
      
      expect(type).toBe('network');
    });

    it('should classify API errors correctly', () => {
      const apiError = new Error('API unauthorized access');
      const type = handler.classifyError(apiError);
      
      expect(type).toBe('api');
    });

    it('should classify rendering errors correctly', () => {
      const renderError = new Error('Map rendering failed');
      const type = handler.classifyError(renderError);
      
      expect(type).toBe('rendering');
    });

    it('should classify data errors correctly', () => {
      const dataError = new Error('Invalid data format');
      const type = handler.classifyError(dataError);
      
      expect(type).toBe('data');
    });

    it('should handle unknown errors', () => {
      const unknownError = new Error('Something weird happened');
      const type = handler.classifyError(unknownError);
      
      expect(type).toBe('unknown');
    });

    it('should track error frequency', () => {
      const error = new Error('Network timeout');
      
      const result1 = handler.handleError(error, 'test-context');
      const result2 = handler.handleError(error, 'test-context');
      
      expect(result1.count).toBe(1);
      expect(result2.count).toBe(2);
    });

    it('should provide appropriate handling strategies', () => {
      const networkError = new Error('Network timeout');
      
      // First few errors should allow retry
      const result1 = handler.handleError(networkError, 'test');
      expect(result1.shouldRetry).toBe(true);
      expect(result1.strategy.fallback).toBe('retry_with_delay');
      
      // After threshold, should switch to fallback
      for (let i = 0; i < 5; i++) {
        handler.handleError(networkError, 'test');
      }
      
      const resultAfterThreshold = handler.handleError(networkError, 'test');
      expect(resultAfterThreshold.shouldRetry).toBe(false);
      expect(resultAfterThreshold.strategy.fallback).toBe('offline_mode');
    });

    it('should provide user-friendly messages', () => {
      const networkError = new Error('fetch failed');
      const result = handler.handleError(networkError, 'test');
      
      expect(result.userMessage).toContain('Connection issue');
      expect(result.userMessage).not.toContain('fetch failed'); // Technical details hidden
    });

    it('should reset error counts', () => {
      const error = new Error('Network timeout');
      handler.handleError(error, 'test');
      
      handler.resetErrors('network');
      
      const stats = handler.getStats();
      expect(Object.keys(stats)).toHaveLength(0);
    });
  });

  describe('DegradationManager', () => {
    let manager;

    beforeEach(() => {
      manager = new DegradationManager();
    });

    it('should start with all features enabled', () => {
      expect(manager.isFeatureEnabled('animations')).toBe(true);
      expect(manager.isFeatureEnabled('highDetailRendering')).toBe(true);
      expect(manager.isFeatureEnabled('realTimeUpdates')).toBe(true);
    });

    it('should apply network degradation', () => {
      manager.applyDegradation('network', 3);
      
      expect(manager.isFeatureEnabled('realTimeUpdates')).toBe(false);
      expect(manager.degradationLevel).toBe(2);
    });

    it('should apply rendering degradation', () => {
      manager.applyDegradation('rendering', 2);
      
      expect(manager.isFeatureEnabled('animations')).toBe(false);
      expect(manager.isFeatureEnabled('advancedTooltips')).toBe(false);
    });

    it('should apply severe rendering degradation', () => {
      manager.applyDegradation('rendering', 4);
      
      expect(manager.isFeatureEnabled('animations')).toBe(false);
      expect(manager.isFeatureEnabled('highDetailRendering')).toBe(false);
      expect(manager.degradationLevel).toBe(3);
    });

    it('should apply performance degradation', () => {
      manager.applyDegradation('performance', 3);
      
      expect(manager.isFeatureEnabled('performanceMonitoring')).toBe(false);
      expect(manager.isFeatureEnabled('animations')).toBe(false);
      expect(manager.isFeatureEnabled('highDetailRendering')).toBe(false);
    });

    it('should provide degradation status', () => {
      manager.applyDegradation('rendering', 2);
      
      const status = manager.getStatus();
      
      expect(status.level).toBeGreaterThan(0);
      expect(status.disabledFeatures).toContain('animations');
      expect(status.disabledFeatures).toContain('advancedTooltips');
      expect(status.enabledFeatures).toContain('realTimeUpdates');
    });

    it('should reset to original state', () => {
      manager.applyDegradation('rendering', 4);
      manager.reset();
      
      expect(manager.isFeatureEnabled('animations')).toBe(true);
      expect(manager.isFeatureEnabled('highDetailRendering')).toBe(true);
      expect(manager.degradationLevel).toBe(0);
    });
  });

  describe('NotificationManager', () => {
    let manager;

    beforeEach(() => {
      manager = new NotificationManager();
      vi.clearAllMocks();
    });

    it('should create notifications', () => {
      const id = manager.show('Test message', 'info', 5000);
      
      expect(id).toBeTruthy();
      expect(manager.notifications).toHaveLength(1);
      expect(manager.notifications[0].message).toBe('Test message');
      expect(manager.notifications[0].type).toBe('info');
    });

    it('should limit number of notifications', () => {
      // Create more notifications than the limit
      for (let i = 0; i < 10; i++) {
        manager.show(`Message ${i}`, 'info');
      }
      
      expect(manager.notifications.length).toBeLessThanOrEqual(manager.maxNotifications);
    });

    it('should remove notifications', () => {
      const id = manager.show('Test message', 'info', 0); // No auto-remove
      
      manager.remove(id);
      
      expect(manager.notifications).toHaveLength(0);
    });

    it('should clear all notifications', () => {
      manager.show('Message 1', 'info');
      manager.show('Message 2', 'warning');
      
      manager.clearAll();
      
      expect(manager.notifications).toHaveLength(0);
    });

    it('should create DOM elements', () => {
      const createElement = vi.fn(() => ({
        id: '',
        className: '',
        innerHTML: '',
        appendChild: vi.fn()
      }));
      
      global.document.createElement = createElement;
      
      manager.show('Test message', 'info');
      
      expect(createElement).toHaveBeenCalledWith('div');
    });
  });

  describe('Integration Tests', () => {
    beforeEach(() => {
      // Reset all managers
      retryManager.retryAttempts.clear();
      errorHandler.resetErrors();
      degradationManager.reset();
      notificationManager.clearAll();
    });

    it('should handle complete error recovery workflow', async () => {
      // Simulate a failing operation
      let attempts = 0;
      const failingOperation = vi.fn(() => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Network timeout');
        }
        return 'success';
      });

      // Execute with retry
      const result = await retryManager.executeWithRetry(
        'integration-test',
        failingOperation,
        {
          shouldRetry: (error) => {
            const errorInfo = errorHandler.handleError(error, 'integration');
            return errorInfo.shouldRetry;
          }
        }
      );

      expect(result).toBe('success');
      expect(attempts).toBe(3);

      // Check that error handler tracked the errors
      const stats = errorHandler.getStats();
      expect(stats.network).toBeTruthy();
    });

    it('should apply degradation after repeated failures', async () => {
      const persistentFailure = vi.fn().mockRejectedValue(new Error('Rendering failed'));

      // Simulate multiple failures
      for (let i = 0; i < 5; i++) {
        try {
          await retryManager.executeWithRetry('render-test', persistentFailure, {
            maxRetries: 0,
            shouldRetry: (error) => {
              const errorInfo = errorHandler.handleError(error, 'render');
              
              // Apply degradation if too many errors
              if (errorInfo.count >= 3) {
                degradationManager.applyDegradation('rendering', errorInfo.count);
              }
              
              return false; // Don't retry for this test
            }
          });
        } catch (error) {
          // Expected to fail
        }
      }

      // Check that degradation was applied
      expect(degradationManager.isFeatureEnabled('animations')).toBe(false);
      expect(degradationManager.degradationLevel).toBeGreaterThan(0);
    });
  });
});
