// Performance optimization utilities for aurora map rendering

/**
 * Layer cache for efficient hemisphere switching
 */
class LayerCache {
  constructor(maxSize = 4) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessOrder = [];
  }

  /**
   * Get cached layer
   * @param {string} key - Cache key
   * @returns {Object|null} Cached layer or null
   */
  get(key) {
    if (this.cache.has(key)) {
      // Update access order (LRU)
      this.accessOrder = this.accessOrder.filter(k => k !== key);
      this.accessOrder.push(key);
      return this.cache.get(key);
    }
    return null;
  }

  /**
   * Set cached layer
   * @param {string} key - Cache key
   * @param {Object} layer - Leaflet layer
   * @param {Object} metadata - Layer metadata
   */
  set(key, layer, metadata = {}) {
    // Remove oldest if cache is full
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      const oldestKey = this.accessOrder.shift();
      const oldLayer = this.cache.get(oldestKey);
      if (oldLayer && oldLayer.layer) {
        this.cleanupLayer(oldLayer.layer);
      }
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      layer,
      metadata: {
        ...metadata,
        createdAt: Date.now(),
        accessCount: 0
      }
    });

    // Update access order
    this.accessOrder = this.accessOrder.filter(k => k !== key);
    this.accessOrder.push(key);
  }

  /**
   * Clear all cached layers
   */
  clear() {
    for (const [key, cached] of this.cache.entries()) {
      if (cached.layer) {
        this.cleanupLayer(cached.layer);
      }
    }
    this.cache.clear();
    this.accessOrder = [];
  }

  /**
   * Clean up a layer and its resources
   * @param {Object} layer - Leaflet layer
   */
  cleanupLayer(layer) {
    if (layer && typeof layer.clearLayers === 'function') {
      layer.clearLayers();
    }
    if (layer && typeof layer.remove === 'function') {
      layer.remove();
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const stats = {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys()),
      totalMemory: 0
    };

    for (const [key, cached] of this.cache.entries()) {
      if (cached.layer && cached.layer.getLayers) {
        stats.totalMemory += cached.layer.getLayers().length;
      }
    }

    return stats;
  }
}

/**
 * Performance monitor for tracking map performance
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      layerRenderTime: [],
      dataFetchTime: [],
      tileLoadTime: [],
      memoryUsage: []
    };
    this.startTimes = new Map();
  }

  /**
   * Start timing an operation
   * @param {string} operation - Operation name
   */
  startTiming(operation) {
    this.startTimes.set(operation, performance.now());
  }

  /**
   * End timing an operation
   * @param {string} operation - Operation name
   * @returns {number} Duration in milliseconds
   */
  endTiming(operation) {
    const startTime = this.startTimes.get(operation);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.addMetric(operation, duration);
      this.startTimes.delete(operation);
      return duration;
    }
    return 0;
  }

  /**
   * Add a metric
   * @param {string} type - Metric type
   * @param {number} value - Metric value
   */
  addMetric(type, value) {
    if (!this.metrics[type]) {
      this.metrics[type] = [];
    }
    
    this.metrics[type].push({
      value,
      timestamp: Date.now()
    });

    // Keep only last 50 measurements
    if (this.metrics[type].length > 50) {
      this.metrics[type] = this.metrics[type].slice(-50);
    }
  }

  /**
   * Get performance statistics
   * @returns {Object} Performance statistics
   */
  getStats() {
    const stats = {};
    
    for (const [type, measurements] of Object.entries(this.metrics)) {
      if (measurements.length === 0) {
        stats[type] = { avg: 0, min: 0, max: 0, count: 0 };
        continue;
      }

      const values = measurements.map(m => m.value);
      stats[type] = {
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        count: values.length,
        recent: values.slice(-5) // Last 5 measurements
      };
    }

    return stats;
  }

  /**
   * Check if performance is degraded
   * @returns {Object} Performance status
   */
  checkPerformance() {
    const stats = this.getStats();
    const issues = [];

    // Check layer render time
    if (stats.layerRenderTime.avg > 1000) {
      issues.push('Slow layer rendering');
    }

    // Check data fetch time
    if (stats.dataFetchTime.avg > 5000) {
      issues.push('Slow data fetching');
    }

    // Check tile load time
    if (stats.tileLoadTime.avg > 3000) {
      issues.push('Slow tile loading');
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      stats
    };
  }
}

/**
 * Data point optimizer for reducing rendering load
 */
export class DataOptimizer {
  /**
   * Optimize aurora data points based on zoom level and viewport
   * @param {Array} points - Aurora data points
   * @param {number} zoom - Current zoom level
   * @param {Object} bounds - Map bounds
   * @param {number} maxPoints - Maximum points to render
   * @returns {Array} Optimized points
   */
  static optimizePoints(points, zoom, bounds, maxPoints = 500) {
    if (!points || points.length === 0) return [];

    // Filter points within viewport bounds
    const visiblePoints = points.filter(point => {
      return point.latitude >= bounds.south &&
             point.latitude <= bounds.north &&
             point.longitude >= bounds.west &&
             point.longitude <= bounds.east;
    });

    // If we have fewer points than the limit, return all
    if (visiblePoints.length <= maxPoints) {
      return visiblePoints;
    }

    // Sort by intensity (highest first)
    const sortedPoints = visiblePoints.sort((a, b) => b.intensity - a.intensity);

    // Use different strategies based on zoom level
    if (zoom <= 3) {
      // Low zoom: aggressive clustering and filtering
      return this.clusterPoints(sortedPoints.slice(0, maxPoints * 0.5), 2.0);
    } else if (zoom <= 5) {
      // Medium zoom: moderate clustering
      return this.clusterPoints(sortedPoints.slice(0, maxPoints * 0.7), 1.0);
    } else {
      // High zoom: minimal clustering, more points
      return this.clusterPoints(sortedPoints.slice(0, maxPoints), 0.5);
    }
  }

  /**
   * Cluster nearby points to reduce rendering load
   * @param {Array} points - Points to cluster
   * @param {number} threshold - Distance threshold for clustering
   * @returns {Array} Clustered points
   */
  static clusterPoints(points, threshold) {
    const clusters = [];
    const processed = new Set();

    for (let i = 0; i < points.length; i++) {
      if (processed.has(i)) continue;

      const cluster = [points[i]];
      processed.add(i);

      for (let j = i + 1; j < points.length; j++) {
        if (processed.has(j)) continue;

        const distance = this.calculateDistance(points[i], points[j]);
        if (distance <= threshold) {
          cluster.push(points[j]);
          processed.add(j);
        }
      }

      // Create representative point for cluster
      if (cluster.length === 1) {
        clusters.push(cluster[0]);
      } else {
        clusters.push(this.createClusterPoint(cluster));
      }
    }

    return clusters;
  }

  /**
   * Create a representative point for a cluster
   * @param {Array} cluster - Points in cluster
   * @returns {Object} Representative point
   */
  static createClusterPoint(cluster) {
    const totalLat = cluster.reduce((sum, p) => sum + p.latitude, 0);
    const totalLng = cluster.reduce((sum, p) => sum + p.longitude, 0);
    const totalIntensity = cluster.reduce((sum, p) => sum + p.intensity, 0);

    return {
      latitude: totalLat / cluster.length,
      longitude: totalLng / cluster.length,
      intensity: totalIntensity / cluster.length,
      clusterSize: cluster.length,
      isCluster: true
    };
  }

  /**
   * Calculate distance between two points
   * @param {Object} point1 - First point
   * @param {Object} point2 - Second point
   * @returns {number} Distance in degrees
   */
  static calculateDistance(point1, point2) {
    const latDiff = point1.latitude - point2.latitude;
    const lngDiff = point1.longitude - point2.longitude;
    return Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
  }
}

/**
 * Memory manager for cleaning up unused resources
 */
export class MemoryManager {
  constructor() {
    this.trackedObjects = new WeakSet();
    this.cleanupCallbacks = [];
  }

  /**
   * Track an object for cleanup
   * @param {Object} obj - Object to track
   * @param {Function} cleanupFn - Cleanup function
   */
  track(obj, cleanupFn) {
    this.trackedObjects.add(obj);
    this.cleanupCallbacks.push({ obj, cleanupFn });
  }

  /**
   * Clean up all tracked objects
   */
  cleanup() {
    for (const { obj, cleanupFn } of this.cleanupCallbacks) {
      try {
        if (this.trackedObjects.has(obj)) {
          cleanupFn(obj);
        }
      } catch (error) {
        console.warn('Error during cleanup:', error);
      }
    }
    this.cleanupCallbacks = [];
  }

  /**
   * Force garbage collection (if available)
   */
  forceGC() {
    if (window.gc) {
      window.gc();
    }
  }
}

// Create global instances
export const layerCache = new LayerCache();
export const performanceMonitor = new PerformanceMonitor();
export const memoryManager = new MemoryManager();

/**
 * Initialize performance monitoring
 */
export function initPerformanceMonitoring() {
  // Monitor memory usage periodically
  setInterval(() => {
    if (performance.memory) {
      performanceMonitor.addMetric('memoryUsage', performance.memory.usedJSHeapSize);
    }
  }, 30000); // Every 30 seconds

  // Log performance stats periodically in development
  if (import.meta.env.DEV) {
    setInterval(() => {
      const stats = performanceMonitor.getStats();
      const cacheStats = layerCache.getStats();
      console.log('Aurora Map Performance:', { performance: stats, cache: cacheStats });
    }, 60000); // Every minute
  }
}

/**
 * Optimize map for better performance
 * @param {Object} map - Leaflet map instance
 */
export function optimizeMapPerformance(map) {
  if (!map) return;

  // Disable animations on low-end devices
  if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
    map.options.zoomAnimation = false;
    map.options.fadeAnimation = false;
    map.options.markerZoomAnimation = false;
  }

  // Optimize tile loading
  map.options.preferCanvas = true;
  map.options.updateWhenZooming = false;
  map.options.updateWhenIdle = true;
}
