// Aurora data processing utilities for NOAA OVATION data
import { fetchAPI } from './api.js';

// NOAA OVATION Aurora API endpoints
const AURORA_ENDPOINTS = {
  OVATION_LATEST: '/aurora-ovation-latest',
  OVATION_FORECAST: '/aurora-ovation-forecast',
  AURORA_NOWCAST: '/aurora-nowcast'
};

/**
 * Convert NOAA's 0-360 longitude system to standard -180/180 system
 * @param {number} longitude360 - Longitude in 0-360 format
 * @returns {number} Longitude in -180/180 format
 */
export function convertLongitude360To180(longitude360) {
  return longitude360 > 180 ? longitude360 - 360 : longitude360;
}

/**
 * Validate aurora data point
 * @param {Array} point - [longitude, latitude, intensity]
 * @returns {boolean} True if valid
 */
export function isValidAuroraPoint(point) {
  if (!Array.isArray(point) || point.length !== 3) return false;
  const [lng, lat, intensity] = point;
  return (
    typeof lng === 'number' && lng >= -180 && lng <= 360 &&
    typeof lat === 'number' && lat >= -90 && lat <= 90 &&
    typeof intensity === 'number' && intensity >= 0
  );
}

/**
 * Get aurora color based on intensity using scientific color mapping
 * @param {number} intensity - Aurora intensity (0-100)
 * @param {string} hemisphere - 'northern' or 'southern'
 * @returns {string} RGBA color string
 */
export function getAuroraColor(intensity, hemisphere = 'northern') {
  // Scientific color mapping based on aurora intensity
  const colors = {
    northern: {
      veryHigh: 'rgba(255, 0, 255, 0.9)',    // Magenta for very high intensity
      high: 'rgba(255, 100, 100, 0.8)',      // Red for high intensity
      moderate: 'rgba(255, 255, 0, 0.7)',    // Yellow for moderate
      low: 'rgba(0, 255, 100, 0.6)',         // Green for low
      veryLow: 'rgba(0, 150, 255, 0.5)'      // Blue for very low
    },
    southern: {
      veryHigh: 'rgba(255, 0, 200, 0.9)',    // Pink-magenta for southern aurora
      high: 'rgba(255, 150, 100, 0.8)',      // Orange-red
      moderate: 'rgba(255, 200, 0, 0.7)',    // Golden yellow
      low: 'rgba(100, 255, 150, 0.6)',       // Light green
      veryLow: 'rgba(100, 200, 255, 0.5)'    // Light blue
    }
  };

  const colorSet = colors[hemisphere] || colors.northern;

  if (intensity >= 80) return colorSet.veryHigh;
  if (intensity >= 60) return colorSet.high;
  if (intensity >= 40) return colorSet.moderate;
  if (intensity >= 20) return colorSet.low;
  if (intensity >= 5) return colorSet.veryLow;
  
  return 'rgba(0, 0, 0, 0)'; // Transparent for very low intensity
}

/**
 * Calculate appropriate radius for aurora intensity
 * @param {number} intensity - Aurora intensity (0-100)
 * @param {number} baseRadius - Base radius in meters
 * @returns {number} Calculated radius in meters
 */
export function calculateAuroraRadius(intensity, baseRadius = 50000) {
  // Scale radius based on intensity with minimum and maximum bounds
  const minRadius = baseRadius * 0.3;
  const maxRadius = baseRadius * 2.5;
  const scaleFactor = Math.pow(intensity / 100, 0.7); // Non-linear scaling
  
  return Math.max(minRadius, Math.min(maxRadius, baseRadius * (0.5 + scaleFactor * 1.5)));
}

/**
 * Process raw NOAA OVATION aurora data
 * @param {Object} rawData - Raw NOAA data
 * @returns {Object} Processed data with northern and southern hemisphere arrays
 */
export function processOvationData(rawData) {
  if (!rawData || !rawData.coordinates || !Array.isArray(rawData.coordinates)) {
    console.warn('Invalid OVATION data structure');
    return { northern: [], southern: [], metadata: null };
  }

  const northern = [];
  const southern = [];
  const processedPoints = new Set(); // Prevent duplicates

  for (const point of rawData.coordinates) {
    if (!isValidAuroraPoint(point)) continue;

    const [longitude360, latitude, intensity] = point;
    const longitude = convertLongitude360To180(longitude360);
    
    // Skip very low intensity points to reduce clutter
    if (intensity < 1) continue;

    // Create unique key to prevent duplicates
    const key = `${longitude.toFixed(2)},${latitude.toFixed(2)}`;
    if (processedPoints.has(key)) continue;
    processedPoints.add(key);

    const processedPoint = {
      longitude,
      latitude,
      intensity,
      color: getAuroraColor(intensity, latitude >= 0 ? 'northern' : 'southern'),
      radius: calculateAuroraRadius(intensity)
    };

    // Separate by hemisphere
    if (latitude >= 0) {
      northern.push(processedPoint);
    } else {
      southern.push(processedPoint);
    }
  }

  // Extract metadata
  const metadata = {
    observationTime: rawData['Observation Time'] || rawData['Forecast Time'] || new Date().toISOString(),
    totalPoints: rawData.coordinates.length,
    processedPoints: northern.length + southern.length,
    northernPoints: northern.length,
    southernPoints: southern.length
  };

  return { northern, southern, metadata };
}

/**
 * Fetch latest aurora data from NOAA OVATION
 * @returns {Promise<Object>} Processed aurora data
 */
export async function fetchLatestAuroraData() {
  try {
    const rawData = await fetchAPI(AURORA_ENDPOINTS.OVATION_LATEST, 'AURORA_OVATION');
    return processOvationData(rawData);
  } catch (error) {
    console.error('Failed to fetch latest aurora data:', error);
    throw new Error('Unable to load aurora forecast data. Please try again later.');
  }
}

/**
 * Fetch aurora forecast data
 * @returns {Promise<Object>} Processed forecast data
 */
export async function fetchAuroraForecast() {
  try {
    const rawData = await fetchAPI(AURORA_ENDPOINTS.OVATION_FORECAST, 'AURORA_FORECAST');
    return processOvationData(rawData);
  } catch (error) {
    console.error('Failed to fetch aurora forecast:', error);
    throw new Error('Unable to load aurora forecast data. Please try again later.');
  }
}

/**
 * Get fallback aurora data for when API is unavailable
 * @param {string} hemisphere - 'northern' or 'southern'
 * @returns {Object} Fallback data structure
 */
export function getFallbackAuroraData(hemisphere = 'both') {
  const fallbackNorthern = [
    { longitude: -100, latitude: 65, intensity: 45, color: getAuroraColor(45, 'northern'), radius: 75000 },
    { longitude: -150, latitude: 70, intensity: 60, color: getAuroraColor(60, 'northern'), radius: 90000 },
    { longitude: 20, latitude: 68, intensity: 35, color: getAuroraColor(35, 'northern'), radius: 65000 },
    { longitude: 100, latitude: 72, intensity: 50, color: getAuroraColor(50, 'northern'), radius: 80000 }
  ];

  const fallbackSouthern = [
    { longitude: 140, latitude: -65, intensity: 40, color: getAuroraColor(40, 'southern'), radius: 70000 },
    { longitude: -60, latitude: -70, intensity: 55, color: getAuroraColor(55, 'southern'), radius: 85000 },
    { longitude: 0, latitude: -68, intensity: 30, color: getAuroraColor(30, 'southern'), radius: 60000 }
  ];

  const metadata = {
    observationTime: new Date().toISOString(),
    totalPoints: 7,
    processedPoints: 7,
    northernPoints: 4,
    southernPoints: 3,
    isFallback: true
  };

  if (hemisphere === 'northern') return { northern: fallbackNorthern, southern: [], metadata };
  if (hemisphere === 'southern') return { northern: [], southern: fallbackSouthern, metadata };
  return { northern: fallbackNorthern, southern: fallbackSouthern, metadata };
}

/**
 * Validate processed aurora data
 * @param {Object} data - Processed aurora data
 * @returns {boolean} True if data is valid
 */
export function validateAuroraData(data) {
  return (
    data &&
    typeof data === 'object' &&
    Array.isArray(data.northern) &&
    Array.isArray(data.southern) &&
    data.metadata &&
    typeof data.metadata === 'object'
  );
}
