// Comprehensive error recovery utilities for aurora map

/**
 * Retry mechanism with exponential backoff
 */
export class RetryManager {
  constructor(maxRetries = 3, baseDelay = 1000, maxDelay = 30000) {
    this.maxRetries = maxRetries;
    this.baseDelay = baseDelay;
    this.maxDelay = maxDelay;
    this.retryAttempts = new Map();
  }

  /**
   * Execute function with retry logic
   * @param {string} key - Unique key for the operation
   * @param {Function} fn - Function to execute
   * @param {Object} options - Retry options
   * @returns {Promise} Result of the function
   */
  async executeWithRetry(key, fn, options = {}) {
    const {
      maxRetries = this.maxRetries,
      baseDelay = this.baseDelay,
      maxDelay = this.maxDelay,
      shouldRetry = (error) => true
    } = options;

    let lastError;
    const attempts = this.retryAttempts.get(key) || 0;

    for (let attempt = attempts; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn();
        // Success - reset retry count
        this.retryAttempts.delete(key);
        return result;
      } catch (error) {
        lastError = error;
        this.retryAttempts.set(key, attempt + 1);

        // Check if we should retry this error
        if (!shouldRetry(error) || attempt >= maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = Math.min(
          baseDelay * Math.pow(2, attempt) + Math.random() * 1000,
          maxDelay
        );

        console.warn(`Attempt ${attempt + 1} failed for ${key}, retrying in ${delay}ms:`, error);
        await this.delay(delay);
      }
    }

    throw lastError;
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Reset retry count for a key
   * @param {string} key - Operation key
   */
  reset(key) {
    this.retryAttempts.delete(key);
  }

  /**
   * Get retry statistics
   * @returns {Object} Retry statistics
   */
  getStats() {
    return {
      activeRetries: this.retryAttempts.size,
      retryAttempts: Object.fromEntries(this.retryAttempts)
    };
  }
}

/**
 * Error classification and handling
 */
export class ErrorHandler {
  constructor() {
    this.errorCounts = new Map();
    this.errorThresholds = {
      network: 5,
      api: 3,
      rendering: 10,
      data: 5
    };
  }

  /**
   * Classify error type
   * @param {Error} error - Error to classify
   * @returns {string} Error type
   */
  classifyError(error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return 'network';
    }
    if (message.includes('api') || message.includes('unauthorized') || message.includes('forbidden')) {
      return 'api';
    }
    if (message.includes('render') || message.includes('layer') || message.includes('map')) {
      return 'rendering';
    }
    if (message.includes('data') || message.includes('parse') || message.includes('invalid')) {
      return 'data';
    }
    
    return 'unknown';
  }

  /**
   * Handle error with appropriate strategy
   * @param {Error} error - Error to handle
   * @param {string} context - Context where error occurred
   * @returns {Object} Error handling result
   */
  handleError(error, context = 'unknown') {
    const errorType = this.classifyError(error);
    const errorKey = `${errorType}-${context}`;
    
    // Track error frequency
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);
    
    // Determine handling strategy
    const strategy = this.getHandlingStrategy(errorType, count);
    
    return {
      type: errorType,
      count: count + 1,
      strategy,
      shouldRetry: strategy.retry,
      fallbackAction: strategy.fallback,
      userMessage: this.getUserMessage(errorType, strategy),
      technicalDetails: error.message
    };
  }

  /**
   * Get error handling strategy
   * @param {string} errorType - Type of error
   * @param {number} count - Number of occurrences
   * @returns {Object} Handling strategy
   */
  getHandlingStrategy(errorType, count) {
    const threshold = this.errorThresholds[errorType] || 3;
    
    const strategies = {
      network: {
        retry: count < threshold,
        fallback: count >= threshold ? 'offline_mode' : 'retry_with_delay',
        degradation: count >= threshold ? 'use_cached_data' : null
      },
      api: {
        retry: count < 2,
        fallback: 'use_fallback_data',
        degradation: 'limited_functionality'
      },
      rendering: {
        retry: count < threshold,
        fallback: 'simplified_rendering',
        degradation: 'reduce_detail_level'
      },
      data: {
        retry: count < 2,
        fallback: 'use_sample_data',
        degradation: 'show_data_warning'
      },
      unknown: {
        retry: count < 1,
        fallback: 'generic_error_message',
        degradation: 'basic_functionality'
      }
    };

    return strategies[errorType] || strategies.unknown;
  }

  /**
   * Get user-friendly error message
   * @param {string} errorType - Type of error
   * @param {Object} strategy - Handling strategy
   * @returns {string} User message
   */
  getUserMessage(errorType, strategy) {
    const messages = {
      network: {
        retry_with_delay: 'Connection issue detected. Retrying...',
        offline_mode: 'Unable to connect. Using cached data.',
        use_cached_data: 'Network unavailable. Showing last known data.'
      },
      api: {
        use_fallback_data: 'Aurora data service unavailable. Using sample data.',
        limited_functionality: 'Some features may be limited due to service issues.'
      },
      rendering: {
        simplified_rendering: 'Using simplified map view for better performance.',
        reduce_detail_level: 'Reducing map detail to improve stability.'
      },
      data: {
        use_sample_data: 'Aurora data temporarily unavailable. Showing example data.',
        show_data_warning: 'Data quality may be affected. Please refresh later.'
      },
      unknown: {
        generic_error_message: 'An unexpected error occurred. Please try again.',
        basic_functionality: 'Some features may not work properly.'
      }
    };

    return messages[errorType]?.[strategy.fallback] || 
           'An error occurred. Please refresh the page.';
  }

  /**
   * Reset error counts
   * @param {string} errorType - Optional error type to reset
   */
  resetErrors(errorType = null) {
    if (errorType) {
      for (const [key] of this.errorCounts) {
        if (key.startsWith(errorType)) {
          this.errorCounts.delete(key);
        }
      }
    } else {
      this.errorCounts.clear();
    }
  }

  /**
   * Get error statistics
   * @returns {Object} Error statistics
   */
  getStats() {
    const stats = {};
    for (const [key, count] of this.errorCounts) {
      const [type, context] = key.split('-');
      if (!stats[type]) stats[type] = {};
      stats[type][context] = count;
    }
    return stats;
  }
}

/**
 * Graceful degradation manager
 */
export class DegradationManager {
  constructor() {
    this.degradationLevel = 0;
    this.features = {
      animations: true,
      highDetailRendering: true,
      realTimeUpdates: true,
      advancedTooltips: true,
      performanceMonitoring: true
    };
    this.originalFeatures = { ...this.features };
  }

  /**
   * Apply degradation based on error severity
   * @param {string} errorType - Type of error
   * @param {number} severity - Error severity (1-5)
   */
  applyDegradation(errorType, severity) {
    switch (errorType) {
      case 'network':
        if (severity >= 3) {
          this.features.realTimeUpdates = false;
          this.degradationLevel = Math.max(this.degradationLevel, 2);
        }
        break;
        
      case 'rendering':
        if (severity >= 2) {
          this.features.animations = false;
          this.features.advancedTooltips = false;
        }
        if (severity >= 4) {
          this.features.highDetailRendering = false;
          this.degradationLevel = Math.max(this.degradationLevel, 3);
        }
        break;
        
      case 'performance':
        this.features.performanceMonitoring = false;
        if (severity >= 3) {
          this.features.animations = false;
          this.features.highDetailRendering = false;
          this.degradationLevel = Math.max(this.degradationLevel, 2);
        }
        break;
    }
  }

  /**
   * Check if feature is enabled
   * @param {string} feature - Feature name
   * @returns {boolean} Whether feature is enabled
   */
  isFeatureEnabled(feature) {
    return this.features[feature] !== false;
  }

  /**
   * Get current degradation status
   * @returns {Object} Degradation status
   */
  getStatus() {
    return {
      level: this.degradationLevel,
      disabledFeatures: Object.keys(this.features).filter(
        key => !this.features[key] && this.originalFeatures[key]
      ),
      enabledFeatures: Object.keys(this.features).filter(
        key => this.features[key]
      )
    };
  }

  /**
   * Reset to original feature set
   */
  reset() {
    this.features = { ...this.originalFeatures };
    this.degradationLevel = 0;
  }
}

/**
 * User notification manager
 */
export class NotificationManager {
  constructor() {
    this.notifications = [];
    this.maxNotifications = 5;
  }

  /**
   * Show notification to user
   * @param {string} message - Notification message
   * @param {string} type - Notification type (info, warning, error)
   * @param {number} duration - Duration in milliseconds
   * @returns {string} Notification ID
   */
  show(message, type = 'info', duration = 5000) {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const notification = {
      id,
      message,
      type,
      timestamp: Date.now(),
      duration
    };

    this.notifications.push(notification);
    
    // Remove old notifications if we exceed the limit
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(-this.maxNotifications);
    }

    // Create DOM element
    this.createNotificationElement(notification);

    // Auto-remove after duration
    if (duration > 0) {
      setTimeout(() => this.remove(id), duration);
    }

    return id;
  }

  /**
   * Remove notification
   * @param {string} id - Notification ID
   */
  remove(id) {
    this.notifications = this.notifications.filter(n => n.id !== id);
    const element = document.getElementById(id);
    if (element) {
      element.remove();
    }
  }

  /**
   * Create notification DOM element
   * @param {Object} notification - Notification object
   */
  createNotificationElement(notification) {
    const container = this.getOrCreateContainer();
    
    const element = document.createElement('div');
    element.id = notification.id;
    element.className = `aurora-notification aurora-notification-${notification.type}`;
    element.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${notification.message}</span>
        <button class="notification-close" onclick="window.auroraNotifications?.remove('${notification.id}')">&times;</button>
      </div>
    `;

    container.appendChild(element);

    // Add animation
    setTimeout(() => element.classList.add('show'), 10);
  }

  /**
   * Get or create notification container
   * @returns {HTMLElement} Container element
   */
  getOrCreateContainer() {
    let container = document.getElementById('aurora-notifications');
    if (!container) {
      container = document.createElement('div');
      container.id = 'aurora-notifications';
      container.className = 'aurora-notifications-container';
      document.body.appendChild(container);
    }
    return container;
  }

  /**
   * Clear all notifications
   */
  clearAll() {
    this.notifications = [];
    const container = document.getElementById('aurora-notifications');
    if (container) {
      container.innerHTML = '';
    }
  }
}

// Create global instances
export const retryManager = new RetryManager();
export const errorHandler = new ErrorHandler();
export const degradationManager = new DegradationManager();
export const notificationManager = new NotificationManager();

// Make notification manager globally available
if (typeof window !== 'undefined') {
  window.auroraNotifications = notificationManager;
}
