// 数据缓存系统 - 根据极光预报网站开发文档实现

const CACHE_CONFIG = {
  KP_DATA: { duration: 10 * 60 * 1000 },           // 10分钟
  FORECAST_DATA: { duration: 60 * 60 * 1000 },     // 1小时
  AURORA_POWER: { duration: 3 * 60 * 1000 },       // 3分钟
  MAGNETIC_FIELD: { duration: 5 * 60 * 1000 },     // 5分钟
  SOLAR_WIND: { duration: 5 * 60 * 1000 },         // 5分钟
  AURORA_OVATION: { duration: 15 * 60 * 1000 },    // 15分钟 - OVATION数据更新频率
  AURORA_FORECAST: { duration: 30 * 60 * 1000 },   // 30分钟 - 预报数据
  AURORA_NOWCAST: { duration: 5 * 60 * 1000 }      // 5分钟 - 实时数据
};

class DataCache {
  constructor() {
    this.cache = new Map();
    this.cleanup();
  }
  
  // 获取缓存或通过fetcher函数获取新数据
  async get(key, fetcher, cacheType = 'KP_DATA') {
    const duration = CACHE_CONFIG[cacheType]?.duration || CACHE_CONFIG.KP_DATA.duration;
    const cached = this.cache.get(key);
    
    // 检查缓存是否有效
    if (cached && Date.now() - cached.timestamp < duration) {
      console.log(`Cache hit for ${key}`);
      return cached.data;
    }
    
    try {
      console.log(`Cache miss for ${key}, fetching new data`);
      const data = await fetcher();
      
      // 存储到缓存
      this.cache.set(key, {
        data,
        timestamp: Date.now()
      });
      
      return data;
    } catch (error) {
      // 如果有过期缓存，在网络错误时返回它
      if (cached) {
        console.warn(`Using stale cache for ${key} due to fetch error:`, error);
        return cached.data;
      }
      throw error;
    }
  }
  
  // 清理过期缓存
  cleanup() {
    setInterval(() => {
      const now = Date.now();
      const maxAge = Math.max(...Object.values(CACHE_CONFIG).map(c => c.duration));
      
      for (const [key, cache] of this.cache.entries()) {
        if (now - cache.timestamp > maxAge * 2) { // 保留2倍时间后清理
          this.cache.delete(key);
          console.log(`Cleaned up expired cache for ${key}`);
        }
      }
    }, 30 * 60 * 1000); // 每30分钟清理一次
  }
  
  // 手动清除特定缓存
  clear(key) {
    this.cache.delete(key);
  }
  
  // 清除所有缓存
  clearAll() {
    this.cache.clear();
  }
  
  // 获取缓存状态
  getCacheStatus() {
    const status = {};
    const now = Date.now();
    
    for (const [key, cache] of this.cache.entries()) {
      const age = now - cache.timestamp;
      status[key] = {
        age: Math.round(age / 1000), // 秒
        isValid: age < (CACHE_CONFIG[key] || CACHE_CONFIG.KP_DATA).duration
      };
    }
    
    return status;
  }
}

// 创建全局缓存实例
export const dataCache = new DataCache();

// 缓存配置导出
export { CACHE_CONFIG }; 