// Advanced aurora visualization utilities for enhanced map rendering

/**
 * Create smooth color gradients for aurora intensity
 * @param {number} intensity - Aurora intensity (0-100)
 * @param {string} hemisphere - 'northern' or 'southern'
 * @returns {Object} Color information with gradients
 */
export function createAuroraGradient(intensity, hemisphere = 'northern') {
  const gradients = {
    northern: {
      colors: [
        { stop: 0, color: [0, 50, 100] },      // Deep blue
        { stop: 20, color: [0, 150, 255] },    // Light blue
        { stop: 40, color: [0, 255, 150] },    // Green
        { stop: 60, color: [255, 255, 0] },    // Yellow
        { stop: 80, color: [255, 100, 0] },    // Orange
        { stop: 100, color: [255, 0, 255] }    // Magenta
      ]
    },
    southern: {
      colors: [
        { stop: 0, color: [100, 0, 100] },     // Purple
        { stop: 20, color: [200, 0, 255] },    // Light purple
        { stop: 40, color: [255, 100, 200] },  // Pink
        { stop: 60, color: [255, 200, 100] },  // Peach
        { stop: 80, color: [255, 150, 0] },    // Orange
        { stop: 100, color: [255, 0, 200] }    // Hot pink
      ]
    }
  };

  const colorSet = gradients[hemisphere] || gradients.northern;
  
  // Find the two colors to interpolate between
  let lowerColor = colorSet.colors[0];
  let upperColor = colorSet.colors[colorSet.colors.length - 1];
  
  for (let i = 0; i < colorSet.colors.length - 1; i++) {
    if (intensity >= colorSet.colors[i].stop && intensity <= colorSet.colors[i + 1].stop) {
      lowerColor = colorSet.colors[i];
      upperColor = colorSet.colors[i + 1];
      break;
    }
  }
  
  // Interpolate between the two colors
  const ratio = (intensity - lowerColor.stop) / (upperColor.stop - lowerColor.stop);
  const r = Math.round(lowerColor.color[0] + (upperColor.color[0] - lowerColor.color[0]) * ratio);
  const g = Math.round(lowerColor.color[1] + (upperColor.color[1] - lowerColor.color[1]) * ratio);
  const b = Math.round(lowerColor.color[2] + (upperColor.color[2] - lowerColor.color[2]) * ratio);
  
  // Calculate opacity based on intensity
  const opacity = Math.min(0.9, Math.max(0.3, intensity / 100 * 0.8 + 0.2));
  
  return {
    fill: `rgba(${r}, ${g}, ${b}, ${opacity})`,
    stroke: `rgba(${r}, ${g}, ${b}, ${Math.min(1, opacity + 0.2)})`,
    rgb: [r, g, b],
    opacity
  };
}

/**
 * Calculate dynamic radius based on intensity and zoom level
 * @param {number} intensity - Aurora intensity (0-100)
 * @param {number} zoom - Current map zoom level
 * @param {number} baseRadius - Base radius in meters
 * @returns {number} Calculated radius
 */
export function calculateDynamicRadius(intensity, zoom, baseRadius = 50000) {
  // Scale factor based on zoom level
  const zoomScale = Math.pow(2, Math.max(0, 8 - zoom)) * 0.5;
  
  // Intensity scale (non-linear for better visual representation)
  const intensityScale = Math.pow(intensity / 100, 0.6);
  
  // Minimum and maximum radius bounds
  const minRadius = baseRadius * 0.2;
  const maxRadius = baseRadius * 3;
  
  const calculatedRadius = baseRadius * intensityScale * zoomScale;
  return Math.max(minRadius, Math.min(maxRadius, calculatedRadius));
}

/**
 * Create aurora visualization layer with enhanced rendering
 * @param {Object} L - Leaflet instance
 * @param {Array} auroraPoints - Array of aurora data points
 * @param {string} hemisphere - 'northern' or 'southern'
 * @param {number} currentZoom - Current map zoom level
 * @returns {Object} Leaflet layer group
 */
export function createEnhancedAuroraLayer(L, auroraPoints, hemisphere, currentZoom = 3) {
  if (!auroraPoints || auroraPoints.length === 0) return null;
  
  const features = [];
  const intensityThreshold = 5; // Minimum intensity to display
  
  // Group nearby points for better performance
  const groupedPoints = groupNearbyPoints(auroraPoints, 0.5); // 0.5 degree grouping
  
  for (const group of groupedPoints) {
    const avgIntensity = group.reduce((sum, p) => sum + p.intensity, 0) / group.length;
    
    if (avgIntensity < intensityThreshold) continue;
    
    const centerPoint = calculateGroupCenter(group);
    const colors = createAuroraGradient(avgIntensity, hemisphere);
    const radius = calculateDynamicRadius(avgIntensity, currentZoom);
    
    // Create main aurora circle
    const circle = L.circle([centerPoint.latitude, centerPoint.longitude], {
      color: colors.stroke,
      fillColor: colors.fill,
      fillOpacity: colors.opacity,
      radius: radius,
      weight: 2,
      opacity: 0.8,
      className: 'aurora-circle'
    });
    
    // Add glow effect for high intensity
    if (avgIntensity > 60) {
      const glowCircle = L.circle([centerPoint.latitude, centerPoint.longitude], {
        color: colors.stroke,
        fillColor: colors.fill,
        fillOpacity: colors.opacity * 0.3,
        radius: radius * 1.5,
        weight: 1,
        opacity: 0.4,
        className: 'aurora-glow'
      });
      features.push(glowCircle);
    }
    
    // Enhanced tooltip with more information
    const tooltipContent = createEnhancedTooltip(group, hemisphere, avgIntensity);
    circle.bindTooltip(tooltipContent, {
      sticky: true,
      className: 'aurora-tooltip-enhanced'
    });
    
    features.push(circle);
  }
  
  return features.length > 0 ? L.layerGroup(features) : null;
}

/**
 * Group nearby aurora points for better visualization
 * @param {Array} points - Aurora data points
 * @param {number} threshold - Distance threshold in degrees
 * @returns {Array} Grouped points
 */
function groupNearbyPoints(points, threshold) {
  const groups = [];
  const processed = new Set();
  
  for (let i = 0; i < points.length; i++) {
    if (processed.has(i)) continue;
    
    const group = [points[i]];
    processed.add(i);
    
    for (let j = i + 1; j < points.length; j++) {
      if (processed.has(j)) continue;
      
      const distance = calculateDistance(points[i], points[j]);
      if (distance <= threshold) {
        group.push(points[j]);
        processed.add(j);
      }
    }
    
    groups.push(group);
  }
  
  return groups;
}

/**
 * Calculate distance between two points in degrees
 * @param {Object} point1 - First point with latitude/longitude
 * @param {Object} point2 - Second point with latitude/longitude
 * @returns {number} Distance in degrees
 */
function calculateDistance(point1, point2) {
  const latDiff = point1.latitude - point2.latitude;
  const lngDiff = point1.longitude - point2.longitude;
  return Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
}

/**
 * Calculate center point of a group
 * @param {Array} group - Group of aurora points
 * @returns {Object} Center point with latitude/longitude
 */
function calculateGroupCenter(group) {
  const totalLat = group.reduce((sum, p) => sum + p.latitude, 0);
  const totalLng = group.reduce((sum, p) => sum + p.longitude, 0);
  
  return {
    latitude: totalLat / group.length,
    longitude: totalLng / group.length
  };
}

/**
 * Create enhanced tooltip content
 * @param {Array} group - Group of aurora points
 * @param {string} hemisphere - 'northern' or 'southern'
 * @param {number} avgIntensity - Average intensity
 * @returns {string} HTML tooltip content
 */
function createEnhancedTooltip(group, hemisphere, avgIntensity) {
  const auroraName = hemisphere === 'northern' ? 'Aurora Borealis' : 'Aurora Australis';
  const emoji = hemisphere === 'northern' ? '🌌' : '🌠';
  
  const intensityLevel = getIntensityLevel(avgIntensity);
  const center = calculateGroupCenter(group);
  
  return `
    <div class="aurora-tooltip-content">
      <div class="tooltip-header">
        <span class="aurora-emoji">${emoji}</span>
        <strong>${auroraName}</strong>
      </div>
      <div class="tooltip-body">
        <div class="intensity-info">
          <strong>Intensity:</strong> ${avgIntensity.toFixed(1)} (${intensityLevel})
        </div>
        <div class="location-info">
          <strong>Location:</strong> ${center.latitude.toFixed(2)}°, ${center.longitude.toFixed(2)}°
        </div>
        <div class="points-info">
          <strong>Data Points:</strong> ${group.length}
        </div>
      </div>
    </div>
  `;
}

/**
 * Get intensity level description
 * @param {number} intensity - Aurora intensity
 * @returns {string} Intensity level description
 */
function getIntensityLevel(intensity) {
  if (intensity >= 80) return 'Extreme';
  if (intensity >= 60) return 'High';
  if (intensity >= 40) return 'Moderate';
  if (intensity >= 20) return 'Low';
  return 'Very Low';
}

/**
 * Create animated aurora effects for high-intensity areas
 * @param {Object} L - Leaflet instance
 * @param {Array} highIntensityPoints - Points with intensity > 70
 * @returns {Object} Animation layer
 */
export function createAuroraAnimationLayer(L, highIntensityPoints) {
  if (!highIntensityPoints || highIntensityPoints.length === 0) return null;
  
  const animatedFeatures = [];
  
  for (const point of highIntensityPoints) {
    if (point.intensity < 70) continue;
    
    // Create pulsing effect
    const pulseCircle = L.circle([point.latitude, point.longitude], {
      color: 'rgba(255, 255, 255, 0.8)',
      fillColor: 'rgba(255, 255, 255, 0.2)',
      fillOpacity: 0.3,
      radius: point.radius * 0.8,
      weight: 2,
      opacity: 0.6,
      className: 'aurora-pulse'
    });
    
    animatedFeatures.push(pulseCircle);
  }
  
  return animatedFeatures.length > 0 ? L.layerGroup(animatedFeatures) : null;
}

/**
 * Update layer visualization based on zoom level
 * @param {Object} layer - Leaflet layer
 * @param {number} newZoom - New zoom level
 * @param {Array} originalData - Original aurora data
 * @param {string} hemisphere - Hemisphere
 */
export function updateLayerForZoom(layer, newZoom, originalData, hemisphere) {
  if (!layer || !originalData) return;
  
  layer.eachLayer((sublayer) => {
    if (sublayer instanceof L.Circle) {
      // Find corresponding data point
      const latlng = sublayer.getLatLng();
      const dataPoint = originalData.find(p => 
        Math.abs(p.latitude - latlng.lat) < 0.1 && 
        Math.abs(p.longitude - latlng.lng) < 0.1
      );
      
      if (dataPoint) {
        const newRadius = calculateDynamicRadius(dataPoint.intensity, newZoom);
        sublayer.setRadius(newRadius);
      }
    }
  });
}
