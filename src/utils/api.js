import { dataCache } from './cache.js';

// API工具函数
const API_BASE_URL = import.meta.env.DEV 
  ? '/api'  // 开发环境使用代理
  : 'https://nodeapi.knockdream.com/api';  // 生产环境直接调用

export function getApiUrl(endpoint) {
  return `${API_BASE_URL}${endpoint}`;
}

// 通用的fetch函数，处理错误
async function fetchAPIData(endpoint) {
  const response = await fetch(getApiUrl(endpoint));
  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status}`);
  }
  return await response.json();
}

// 带缓存的API调用
export async function fetchAPI(endpoint, cacheType) {
  try {
    return await dataCache.get(
      endpoint, 
      () => fetchAPIData(endpoint),
      cacheType
    );
  } catch (error) {
    console.error(`获取${endpoint}数据失败:`, error);
    throw error;
  }
}

// 特定API的便捷函数
export const fetchKpIndex = () => fetchAPI('/kp-index', 'KP_DATA');
export const fetchAuroraForecast = () => fetchAPI('/aurora-forecast', 'FORECAST_DATA');
export const fetchAuroraPower = () => fetchAPI('/aurora-power', 'AURORA_POWER');
export const fetchMagneticField = () => fetchAPI('/magnetic-field', 'MAGNETIC_FIELD');
export const fetchSolarWind = () => fetchAPI('/solar-wind', 'SOLAR_WIND');

// Aurora-specific API functions
export const fetchAuroraOvationLatest = () => fetchAPI('/aurora-ovation-latest', 'AURORA_OVATION');
export const fetchAuroraOvationForecast = () => fetchAPI('/aurora-ovation-forecast', 'AURORA_FORECAST');
export const fetchAuroraNowcast = () => fetchAPI('/aurora-nowcast', 'AURORA_NOWCAST');