// Map utilities for robust tile loading and error handling

/**
 * Tile server configurations with fallbacks
 */
export const TILE_SERVERS = {
  primary: {
    url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    subdomains: 'abcd',
    maxZoom: 19,
    timeout: 10000
  },
  fallback1: {
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    subdomains: 'abc',
    maxZoom: 19,
    timeout: 15000
  },
  fallback2: {
    url: 'https://server.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Dark_Gray_Base/MapServer/tile/{z}/{y}/{x}',
    attribution: 'Tiles &copy; Esri &mdash; <PERSON><PERSON><PERSON>, DeLorme, NAVTEQ',
    maxZoom: 16,
    timeout: 20000
  }
};

/**
 * Map configuration options
 */
export const MAP_CONFIG = {
  defaultCenter: [65, -100],
  defaultZoom: 3,
  minZoom: 2,
  maxZoom: 8,
  maxBounds: [[-90, -180], [90, 180]],
  zoomControl: true,
  attributionControl: true
};

/**
 * Create a tile layer with error handling and retry logic
 * @param {Object} L - Leaflet instance
 * @param {Object} serverConfig - Tile server configuration
 * @param {Function} onError - Error callback
 * @param {Function} onLoad - Load success callback
 * @returns {Object} Leaflet tile layer
 */
export function createRobustTileLayer(L, serverConfig, onError = null, onLoad = null) {
  const layer = L.tileLayer(serverConfig.url, {
    attribution: serverConfig.attribution,
    subdomains: serverConfig.subdomains || '',
    maxZoom: serverConfig.maxZoom || 18,
    timeout: serverConfig.timeout || 10000,
    errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
    retryDelay: 1000,
    maxRetries: 3
  });

  // Track failed tiles for retry logic
  const failedTiles = new Map();
  const retryTimeouts = new Map();

  // Override the createTile method to add retry logic
  const originalCreateTile = layer.createTile;
  layer.createTile = function(coords, done) {
    const tile = originalCreateTile.call(this, coords, done);
    const tileKey = `${coords.x}-${coords.y}-${coords.z}`;
    
    // Set up timeout for tile loading
    const timeoutId = setTimeout(() => {
      if (!tile.complete) {
        handleTileError(tile, tileKey, coords, done);
      }
    }, serverConfig.timeout || 10000);

    tile.onload = function() {
      clearTimeout(timeoutId);
      failedTiles.delete(tileKey);
      if (retryTimeouts.has(tileKey)) {
        clearTimeout(retryTimeouts.get(tileKey));
        retryTimeouts.delete(tileKey);
      }
      if (onLoad) onLoad(coords);
    };

    tile.onerror = function() {
      clearTimeout(timeoutId);
      handleTileError(tile, tileKey, coords, done);
    };

    return tile;
  };

  function handleTileError(tile, tileKey, coords, done) {
    const retryCount = failedTiles.get(tileKey) || 0;
    
    if (retryCount < (layer.options.maxRetries || 3)) {
      // Retry with exponential backoff
      const delay = layer.options.retryDelay * Math.pow(2, retryCount);
      failedTiles.set(tileKey, retryCount + 1);
      
      const retryTimeout = setTimeout(() => {
        console.log(`Retrying tile ${tileKey}, attempt ${retryCount + 1}`);
        tile.src = tile.src.split('?')[0] + '?retry=' + Date.now();
      }, delay);
      
      retryTimeouts.set(tileKey, retryTimeout);
    } else {
      // Max retries reached, use error tile
      console.warn(`Failed to load tile ${tileKey} after ${retryCount} retries`);
      failedTiles.delete(tileKey);
      if (onError) onError(coords, retryCount);
    }
  }

  return layer;
}

/**
 * Initialize map with fallback tile servers
 * @param {Object} L - Leaflet instance
 * @param {string} containerId - Map container element ID
 * @param {Object} options - Map options
 * @returns {Promise<Object>} Map instance and tile layer
 */
export async function initializeRobustMap(L, containerId, options = {}) {
  const mapOptions = { ...MAP_CONFIG, ...options };
  const container = document.getElementById(containerId);
  
  if (!container) {
    throw new Error(`Map container with ID '${containerId}' not found`);
  }

  // Create map instance
  const map = L.map(container, {
    center: mapOptions.defaultCenter,
    zoom: mapOptions.defaultZoom,
    minZoom: mapOptions.minZoom,
    maxZoom: mapOptions.maxZoom,
    maxBounds: mapOptions.maxBounds,
    zoomControl: mapOptions.zoomControl,
    attributionControl: mapOptions.attributionControl
  });

  // Track tile loading status
  let currentTileLayer = null;
  let tileLoadErrors = 0;
  const maxTileErrors = 10;

  // Try primary tile server first
  try {
    currentTileLayer = await loadTileLayer(L, map, TILE_SERVERS.primary);
    console.log('Primary tile server loaded successfully');
  } catch (error) {
    console.warn('Primary tile server failed, trying fallback 1:', error);
    
    try {
      currentTileLayer = await loadTileLayer(L, map, TILE_SERVERS.fallback1);
      console.log('Fallback tile server 1 loaded successfully');
    } catch (error2) {
      console.warn('Fallback 1 failed, trying fallback 2:', error2);
      
      try {
        currentTileLayer = await loadTileLayer(L, map, TILE_SERVERS.fallback2);
        console.log('Fallback tile server 2 loaded successfully');
      } catch (error3) {
        console.error('All tile servers failed:', error3);
        throw new Error('Unable to load map tiles. Please check your internet connection.');
      }
    }
  }

  // Set up tile error monitoring
  function onTileError(coords, retryCount) {
    tileLoadErrors++;
    if (tileLoadErrors > maxTileErrors) {
      console.warn('Too many tile errors, map may not display correctly');
      // Could trigger a notification to the user here
    }
  }

  function onTileLoad(coords) {
    // Reset error count on successful loads
    if (tileLoadErrors > 0) {
      tileLoadErrors = Math.max(0, tileLoadErrors - 1);
    }
  }

  return { map, tileLayer: currentTileLayer };
}

/**
 * Load a tile layer with promise-based error handling
 * @param {Object} L - Leaflet instance
 * @param {Object} map - Map instance
 * @param {Object} serverConfig - Tile server configuration
 * @returns {Promise<Object>} Tile layer
 */
function loadTileLayer(L, map, serverConfig) {
  return new Promise((resolve, reject) => {
    let loadedTiles = 0;
    let errorTiles = 0;
    const testTileCount = 4; // Test loading a few tiles
    let timeoutId;

    const tileLayer = createRobustTileLayer(
      L,
      serverConfig,
      (coords, retryCount) => {
        errorTiles++;
        if (errorTiles >= testTileCount) {
          clearTimeout(timeoutId);
          reject(new Error(`Too many tile errors: ${errorTiles}`));
        }
      },
      (coords) => {
        loadedTiles++;
        if (loadedTiles >= testTileCount) {
          clearTimeout(timeoutId);
          resolve(tileLayer);
        }
      }
    );

    // Add to map
    tileLayer.addTo(map);

    // Set timeout for tile loading test
    timeoutId = setTimeout(() => {
      if (loadedTiles === 0) {
        map.removeLayer(tileLayer);
        reject(new Error('Tile loading timeout'));
      } else {
        // Some tiles loaded, consider it successful
        resolve(tileLayer);
      }
    }, serverConfig.timeout || 10000);
  });
}

/**
 * Create loading indicator for map
 * @param {string} containerId - Container element ID
 * @returns {Object} Loading control functions
 */
export function createMapLoadingIndicator(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return { show: () => {}, hide: () => {} };

  const loader = document.createElement('div');
  loader.className = 'map-loading-indicator';
  loader.innerHTML = `
    <div class="loading-spinner"></div>
    <div class="loading-text">Loading map...</div>
  `;
  
  // Add styles
  loader.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 26, 26, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  `;

  const spinner = loader.querySelector('.loading-spinner');
  spinner.style.cssText = `
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  `;

  const text = loader.querySelector('.loading-text');
  text.style.cssText = `
    color: #e5e7eb;
    font-size: 14px;
    font-weight: 500;
  `;

  // Add keyframe animation
  if (!document.querySelector('#map-loading-styles')) {
    const style = document.createElement('style');
    style.id = 'map-loading-styles';
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  }

  return {
    show: (message = 'Loading map...') => {
      text.textContent = message;
      container.appendChild(loader);
    },
    hide: () => {
      if (loader.parentNode) {
        loader.parentNode.removeChild(loader);
      }
    },
    updateMessage: (message) => {
      text.textContent = message;
    }
  };
}
