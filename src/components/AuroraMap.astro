---
---
<section id="aurora-map" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">Aurora Visibility Map</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Real-time aurora probability map showing where the Northern and Southern Lights are most likely to appear.
      </p>
    </div>
    
    <div class="card p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold">Aurora Probability</h3>
        <div class="flex space-x-2">
          <button id="btn-northern" class="tab active">Northern Hemisphere</button>
          <button id="btn-southern" class="tab">Southern Hemisphere</button>
        </div>
      </div>
      
      <!-- Map Container -->
      <div id="aurora-map-container" class="h-96 rounded-lg overflow-hidden bg-dark-900 relative">
        <div id="map-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm z-10">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
        </div>
        <div id="aurora-map-element" class="w-full h-full"></div>

        <!-- Data Info Overlay -->
        <div class="absolute bottom-2 left-2 bg-dark-800/90 backdrop-blur-sm rounded px-3 py-2 text-xs">
          <div class="text-gray-300 flex items-center justify-between">
            <span>Last updated: <span id="data-timestamp" class="text-green-400">Loading...</span></span>
            <button
              id="manual-refresh-btn"
              class="ml-3 px-2 py-1 bg-primary-600 hover:bg-primary-700 text-white text-xs rounded transition-colors"
              title="Refresh aurora data"
            >
              Refresh
            </button>
          </div>
          <div id="data-statistics" class="mt-1">
            <!-- Statistics will be populated by JavaScript -->
          </div>
        </div>
      </div>
      
      <!-- Map Legend -->
      <div class="mt-6 p-4 bg-dark-700/50 rounded-lg">
        <h4 class="font-semibold mb-3">Aurora Probability Legend</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 64, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">Very Low (0-2%)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 255, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">Low (2-10%)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 255, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">Moderate (10-50%)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 0, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">High (50%+)</span>
          </div>
        </div>
      </div>
      
      <!-- Map Information -->
      <div class="mt-6 p-4 bg-primary-900/30 rounded-lg">
        <h4 class="font-semibold mb-2">How to Use the Map:</h4>
        <ul class="text-sm text-gray-300 space-y-1">
          <li>• Colored regions show aurora visibility probability</li>
          <li>• Higher latitudes typically have better aurora chances</li>
          <li>• Red and orange areas indicate the best viewing locations</li>
          <li>• Green areas may have weak aurora visible to cameras</li>
          <li>• Switch between Northern and Southern Hemispheres using the tabs</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<script>
  // Import utilities dynamically to avoid SSR issues
  let L: any = null;
  let auroraMap: any = null;
  let currentLayer: any = null;
  let currentHemisphere = 'north';
  let auroraData: any = null;
  let loadingIndicator: any = null;
  let dataRefreshInterval: any = null;

  // Import aurora data utilities
  let auroraUtils: any = null;
  let mapUtils: any = null;
  let auroraViz: any = null;
  let mapPerf: any = null;
  let errorRecovery: any = null;

  // Load aurora data from API with comprehensive error recovery
  async function loadAuroraData() {
    if (!errorRecovery) {
      errorRecovery = await import('../utils/errorRecovery.js');
    }

    if (!auroraUtils) {
      auroraUtils = await import('../utils/auroraData.js');
    }

    // Use retry manager for robust data fetching
    return await errorRecovery.retryManager.executeWithRetry(
      'aurora-data-fetch',
      async () => {
        try {
          // Start performance timing
          if (mapPerf) {
            mapPerf.performanceMonitor.startTiming('dataFetchTime');
          }

          const data = await auroraUtils.fetchLatestAuroraData();

          if (auroraUtils.validateAuroraData(data)) {
            auroraData = data;
            updateDataTimestamp(data.metadata.observationTime);

            // End performance timing
            if (mapPerf) {
              mapPerf.performanceMonitor.endTiming('dataFetchTime');
            }

            // Reset error counts on success
            errorRecovery.errorHandler.resetErrors('network');
            errorRecovery.errorHandler.resetErrors('api');

            return data;
          } else {
            throw new Error('Invalid aurora data received');
          }
        } catch (error) {
          // Handle error with comprehensive error recovery
          const errorInfo = errorRecovery.errorHandler.handleError(error, 'data-fetch');

          if (errorInfo.strategy.fallback === 'use_fallback_data' ||
              errorInfo.strategy.fallback === 'use_sample_data') {

            // Show user notification
            errorRecovery.notificationManager.show(
              errorInfo.userMessage,
              'warning',
              8000
            );

            // Use fallback data
            auroraData = auroraUtils.getFallbackAuroraData();
            updateDataTimestamp(new Date().toISOString(), true);

            // Apply degradation if needed
            if (errorInfo.count >= 3) {
              errorRecovery.degradationManager.applyDegradation('network', 3);
            }

            return auroraData;
          }

          // Re-throw if we should retry
          throw error;
        }
      },
      {
        maxRetries: 3,
        baseDelay: 2000,
        maxDelay: 15000,
        shouldRetry: (error) => {
          const errorInfo = errorRecovery.errorHandler.handleError(error, 'data-fetch');
          return errorInfo.shouldRetry;
        }
      }
    );
  }

  // Update data timestamp display
  function updateDataTimestamp(timestamp: string, isFallback = false) {
    const timestampEl = document.getElementById('data-timestamp');
    if (timestampEl) {
      const date = new Date(timestamp);
      const formattedTime = date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
      });

      timestampEl.textContent = isFallback
        ? `${formattedTime} (Fallback Data)`
        : formattedTime;

      if (isFallback) {
        timestampEl.style.color = '#fbbf24'; // Yellow for fallback
      } else {
        timestampEl.style.color = '#10b981'; // Green for real data
      }
    }
  }

  // Setup intelligent data refresh system
  function setupDataRefresh() {
    let refreshInterval = 15 * 60 * 1000; // Start with 15 minutes
    let consecutiveFailures = 0;
    let lastSuccessfulUpdate = Date.now();

    const performRefresh = async () => {
      try {
        // Check if degradation manager suggests disabling real-time updates
        if (errorRecovery && !errorRecovery.degradationManager.isFeatureEnabled('realTimeUpdates')) {
          console.log('Real-time updates disabled due to degradation');
          return;
        }

        // Show subtle loading indicator
        const timestampEl = document.getElementById('data-timestamp');
        if (timestampEl) {
          timestampEl.style.opacity = '0.6';
        }

        const oldData = auroraData;
        await loadAuroraData();

        // Check if data actually changed
        const dataChanged = !oldData ||
          oldData.metadata.observationTime !== auroraData.metadata.observationTime;

        if (dataChanged) {
          // Smooth transition to new data
          await showHemisphere(currentHemisphere);

          // Show update notification
          if (errorRecovery) {
            errorRecovery.notificationManager.show(
              'Aurora data updated',
              'info',
              3000
            );
          }

          console.log('Aurora data refreshed successfully');
          lastSuccessfulUpdate = Date.now();
          consecutiveFailures = 0;

          // Reset refresh interval on success
          refreshInterval = 15 * 60 * 1000;
        }

        // Restore timestamp opacity
        if (timestampEl) {
          timestampEl.style.opacity = '1';
        }

      } catch (error) {
        console.warn('Failed to refresh aurora data:', error);
        consecutiveFailures++;

        // Increase refresh interval on failures (exponential backoff)
        refreshInterval = Math.min(refreshInterval * 1.5, 60 * 60 * 1000); // Max 1 hour

        // Show error notification after multiple failures
        if (consecutiveFailures >= 3 && errorRecovery) {
          errorRecovery.notificationManager.show(
            'Aurora data updates temporarily unavailable',
            'warning',
            5000
          );
        }

        // Restore timestamp opacity
        const timestampEl = document.getElementById('data-timestamp');
        if (timestampEl) {
          timestampEl.style.opacity = '1';
        }
      }
    };

    // Initial refresh setup
    const scheduleNextRefresh = () => {
      if (dataRefreshInterval) {
        clearTimeout(dataRefreshInterval);
      }

      dataRefreshInterval = setTimeout(async () => {
        await performRefresh();
        scheduleNextRefresh(); // Schedule next refresh
      }, refreshInterval);
    };

    // Start the refresh cycle
    scheduleNextRefresh();

    // Add manual refresh capability
    setupManualRefresh();
  }

  // Setup manual refresh button
  function setupManualRefresh() {
    const refreshBtn = document.getElementById('manual-refresh-btn') as HTMLButtonElement;
    if (refreshBtn) {
      refreshBtn.addEventListener('click', async () => {
        refreshBtn.disabled = true;
        refreshBtn.textContent = 'Refreshing...';

        try {
          await loadAuroraData();
          await showHemisphere(currentHemisphere);

          if (errorRecovery) {
            errorRecovery.notificationManager.show(
              'Data refreshed manually',
              'info',
              2000
            );
          }
        } catch (error) {
          console.error('Manual refresh failed:', error);
          if (errorRecovery) {
            errorRecovery.notificationManager.show(
              'Failed to refresh data',
              'error',
              3000
            );
          }
        } finally {
          refreshBtn.disabled = false;
          refreshBtn.textContent = 'Refresh';
        }
      });
    }
  }
  
  // Initialize the map with robust error handling
  async function initAuroraMap() {
    try {
      // Load required modules
      L = await import('leaflet');
      mapUtils = await import('../utils/mapUtils.js');
      mapPerf = await import('../utils/mapPerformance.js');

      // Initialize performance monitoring
      mapPerf.initPerformanceMonitoring();
      mapPerf.performanceMonitor.startTiming('mapInitialization');

      // Create loading indicator
      loadingIndicator = mapUtils.createMapLoadingIndicator('aurora-map-container');
      loadingIndicator.show('Initializing map...');

      // Initialize robust map with fallback tile servers
      const { map, tileLayer } = await mapUtils.initializeRobustMap(
        L,
        'aurora-map-element',
        {
          defaultCenter: [65, -100],
          defaultZoom: 3,
          minZoom: 2,
          maxZoom: 8
        }
      );

      auroraMap = map;

      // Apply performance optimizations
      mapPerf.optimizeMapPerformance(auroraMap);
      loadingIndicator.updateMessage('Loading aurora data...');

      // Load aurora data
      await loadAuroraData();

      // Show northern hemisphere by default
      await showHemisphere('north');

      // Set up automatic data refresh with smart intervals
      setupDataRefresh();

      // Hide loader
      loadingIndicator.hide();

    } catch (error: any) {
      console.error('Error initializing aurora map:', error);

      // Handle error with comprehensive error recovery
      if (errorRecovery) {
        const errorInfo = errorRecovery.errorHandler.handleError(error, 'map-initialization');

        // Show user notification
        errorRecovery.notificationManager.show(
          errorInfo.userMessage,
          'error',
          10000
        );

        // Apply degradation based on error type
        if (errorInfo.type === 'rendering') {
          errorRecovery.degradationManager.applyDegradation('rendering', 4);
        }
      }

      // Show error message to user
      const errorMsg = document.createElement('div');
      errorMsg.className = 'map-error-message';
      errorMsg.innerHTML = `
        <div class="text-center p-6">
          <div class="text-red-400 text-lg mb-2">⚠️ Map Loading Error</div>
          <div class="text-gray-300 text-sm mb-4">${error.message || 'Unable to initialize map'}</div>
          <button onclick="location.reload()" class="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700">
            Retry
          </button>
        </div>
      `;

      const container = document.getElementById('aurora-map-element');
      if (container) {
        container.appendChild(errorMsg);
      }

      if (loadingIndicator) loadingIndicator.hide();
    }
  }
  
  // Show hemisphere data with enhanced visualization and caching
  async function showHemisphere(hemisphere: string) {
    if (!auroraMap || !L || !auroraData) return;

    // Load visualization utilities if not already loaded
    if (!auroraViz) {
      auroraViz = await import('../utils/auroraVisualization.js');
    }

    // Start performance timing
    mapPerf.performanceMonitor.startTiming('layerRenderTime');

    // Generate cache key
    const cacheKey = `${hemisphere}-${auroraData.metadata.observationTime}-${auroraMap.getZoom()}`;

    // Check cache first
    const cachedLayer = mapPerf.layerCache.get(cacheKey);
    if (cachedLayer) {
      // Clear existing layer
      if (currentLayer) {
        auroraMap.removeLayer(currentLayer);
        if (currentLayer._animationLayer) {
          auroraMap.removeLayer(currentLayer._animationLayer);
        }
      }

      // Use cached layer
      currentLayer = cachedLayer.layer;
      currentLayer.addTo(auroraMap);

      if (cachedLayer.metadata.animationLayer) {
        cachedLayer.metadata.animationLayer.addTo(auroraMap);
        currentLayer._animationLayer = cachedLayer.metadata.animationLayer;
      }

      mapPerf.performanceMonitor.endTiming('layerRenderTime');
      updateDataStatistics(cachedLayer.metadata.pointCount, auroraData.metadata);
      return;
    }

    // Clear existing layer
    if (currentLayer) {
      auroraMap.removeLayer(currentLayer);
      if (currentLayer._animationLayer) {
        auroraMap.removeLayer(currentLayer._animationLayer);
      }
      currentLayer = null;
    }

    // Get data for the selected hemisphere
    const hemisphereData = hemisphere === 'north' ? auroraData.northern : auroraData.southern;

    if (hemisphereData.length === 0) {
      // No data available, show default view
      if (hemisphere === 'north') {
        auroraMap.setView([70, -100], 3);
      } else {
        auroraMap.setView([-65, 0], 3);
      }
      mapPerf.performanceMonitor.endTiming('layerRenderTime');
      updateDataStatistics(0, auroraData.metadata);
      return;
    }

    // Optimize data points for current zoom and viewport
    const currentZoom = auroraMap.getZoom();
    const bounds = auroraMap.getBounds();
    const mapBounds = {
      north: bounds.getNorth(),
      south: bounds.getSouth(),
      east: bounds.getEast(),
      west: bounds.getWest()
    };

    const optimizedData = mapPerf.DataOptimizer.optimizePoints(
      hemisphereData,
      currentZoom,
      mapBounds,
      500 // Max points to render
    );

    // Create enhanced aurora layer with optimized data
    const enhancedLayer = auroraViz.createEnhancedAuroraLayer(
      L,
      optimizedData,
      hemisphere === 'north' ? 'northern' : 'southern',
      currentZoom
    );

    if (enhancedLayer) {
      currentLayer = enhancedLayer;
      currentLayer.addTo(auroraMap);

      // Create animation layer for high-intensity areas
      const highIntensityPoints = optimizedData.filter((p: any) => p.intensity > 70);
      let animationLayer = null;
      if (highIntensityPoints.length > 0) {
        animationLayer = auroraViz.createAuroraAnimationLayer(L, highIntensityPoints);
        if (animationLayer) {
          animationLayer.addTo(auroraMap);
          // Store animation layer for cleanup
          currentLayer._animationLayer = animationLayer;
        }
      }

      // Cache the layer for future use
      mapPerf.layerCache.set(cacheKey, currentLayer, {
        pointCount: optimizedData.length,
        hemisphere,
        zoom: currentZoom,
        animationLayer
      });

      // Fit bounds to show all aurora activity
      const allLayers = currentLayer.getLayers();
      if (allLayers.length > 0) {
        const group = L.featureGroup(allLayers);
        auroraMap.fitBounds(group.getBounds(), {
          padding: [30, 30],
          maxZoom: 6
        });
      }

      // Set up zoom event listener for dynamic radius updates
      auroraMap.off('zoomend', onZoomChange); // Remove previous listener
      auroraMap.on('zoomend', onZoomChange);

    } else {
      // Fallback to default view if no layer created
      if (hemisphere === 'north') {
        auroraMap.setView([70, -100], 3);
      } else {
        auroraMap.setView([-65, 0], 3);
      }
    }

    currentHemisphere = hemisphere;

    // End performance timing
    const renderTime = mapPerf.performanceMonitor.endTiming('layerRenderTime');
    console.log(`Layer rendered in ${renderTime.toFixed(2)}ms`);

    // Update data statistics
    updateDataStatistics(optimizedData.length, auroraData.metadata);
  }

  // Handle zoom changes for dynamic visualization updates
  function onZoomChange() {
    if (!currentLayer || !auroraData || !auroraViz) return;

    const hemisphereData = currentHemisphere === 'north' ? auroraData.northern : auroraData.southern;
    const newZoom = auroraMap.getZoom();

    // Update layer visualization for new zoom level
    auroraViz.updateLayerForZoom(currentLayer, newZoom, hemisphereData, currentHemisphere);
  }

  // Update data statistics display
  function updateDataStatistics(pointCount: number, metadata: any) {
    const statsEl = document.getElementById('data-statistics');
    if (statsEl && metadata) {
      const totalPoints = metadata.northernPoints + metadata.southernPoints;
      statsEl.innerHTML = `
        <div class="text-xs text-gray-400">
          Showing ${pointCount} aurora points (${totalPoints} total)
          ${metadata.isFallback ? ' • Using fallback data' : ''}
        </div>
      `;
    }
  }
  
  // Setup event listeners
  function setupEventListeners() {
    const northBtn = document.getElementById('btn-northern');
    const southBtn = document.getElementById('btn-southern');

    if (northBtn && southBtn) {
      northBtn.addEventListener('click', async () => {
        if (currentHemisphere === 'north') return; // Already showing north

        northBtn.classList.add('active');
        southBtn.classList.remove('active');

        try {
          await showHemisphere('north');
        } catch (error) {
          console.error('Error switching to northern hemisphere:', error);
        }
      });

      southBtn.addEventListener('click', async () => {
        if (currentHemisphere === 'south') return; // Already showing south

        southBtn.classList.add('active');
        northBtn.classList.remove('active');

        try {
          await showHemisphere('south');
        } catch (error) {
          console.error('Error switching to southern hemisphere:', error);
        }
      });
    }
  }

  // Cleanup function for when component is destroyed
  function cleanup() {
    if (dataRefreshInterval) {
      clearInterval(dataRefreshInterval);
      dataRefreshInterval = null;
    }

    if (currentLayer && auroraMap) {
      // Remove animation layer if it exists
      if (currentLayer._animationLayer) {
        auroraMap.removeLayer(currentLayer._animationLayer);
      }
      auroraMap.removeLayer(currentLayer);
      currentLayer = null;
    }

    if (auroraMap) {
      // Remove all event listeners
      auroraMap.off('zoomend', onZoomChange);
      auroraMap.remove();
      auroraMap = null;
    }

    // Clean up performance monitoring and caches
    if (mapPerf) {
      mapPerf.layerCache.clear();
      mapPerf.memoryManager.cleanup();
    }
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    initAuroraMap();
  });

  // Cleanup when page is unloaded
  window.addEventListener('beforeunload', cleanup);

  // Also cleanup when navigating away (for SPA behavior)
  document.addEventListener('astro:before-preparation', cleanup);
</script>

<style>
  /* Tab styles */
  .tab {
    @apply px-4 py-2 rounded-md text-sm font-medium transition-all duration-200;
    @apply bg-dark-700 text-gray-300 hover:bg-dark-600 hover:text-white;
  }

  .tab.active {
    @apply bg-primary-600 text-white;
  }

  /* Aurora tooltip styles */
  .aurora-tooltip {
    font-size: 12px;
    line-height: 1.4;
    color: #f3f4f6;
  }

  /* Enhanced aurora tooltip */
  .aurora-tooltip-enhanced {
    background: rgba(17, 24, 39, 0.95) !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
    border-radius: 8px !important;
    backdrop-filter: blur(8px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  .aurora-tooltip-content {
    padding: 8px;
    font-size: 12px;
    line-height: 1.4;
  }

  .tooltip-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
    font-weight: 600;
    color: #e5e7eb;
  }

  .aurora-emoji {
    font-size: 14px;
  }

  .tooltip-body {
    color: #d1d5db;
  }

  .tooltip-body > div {
    margin-bottom: 3px;
  }

  .tooltip-body > div:last-child {
    margin-bottom: 0;
  }

  /* Aurora circle animations */
  .aurora-circle {
    transition: all 0.3s ease-in-out;
  }

  .aurora-circle:hover {
    transform: scale(1.1);
  }

  .aurora-glow {
    animation: aurora-glow 3s ease-in-out infinite alternate;
  }

  .aurora-pulse {
    animation: aurora-pulse 2s ease-in-out infinite;
  }

  @keyframes aurora-glow {
    0% { opacity: 0.2; }
    100% { opacity: 0.6; }
  }

  @keyframes aurora-pulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.6;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.3;
    }
  }

  /* Map error message styles */
  .map-error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(17, 24, 39, 0.95);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(8px);
    z-index: 1000;
  }

  /* Loading animation for map tiles */
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .leaflet-tile-loading {
    animation: pulse 1.5s ease-in-out infinite;
  }

  /* Global notification styles */
  :global(.aurora-notifications-container) {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
  }

  :global(.aurora-notification) {
    background: rgba(17, 24, 39, 0.95);
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 12px 16px;
    min-width: 300px;
    max-width: 400px;
    backdrop-filter: blur(8px);
    border-left: 4px solid #3b82f6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-in-out;
    pointer-events: auto;
  }

  :global(.aurora-notification.show) {
    transform: translateX(0);
    opacity: 1;
  }

  :global(.aurora-notification-warning) {
    border-left-color: #f59e0b;
  }

  :global(.aurora-notification-error) {
    border-left-color: #ef4444;
  }

  :global(.aurora-notification-info) {
    border-left-color: #3b82f6;
  }

  :global(.notification-content) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #e5e7eb;
    font-size: 14px;
    line-height: 1.4;
  }

  :global(.notification-message) {
    flex: 1;
    margin-right: 12px;
  }

  :global(.notification-close) {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  :global(.notification-close:hover) {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
  }
</style>