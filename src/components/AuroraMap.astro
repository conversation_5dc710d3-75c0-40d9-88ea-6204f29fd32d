---
---
<section id="aurora-map" class="py-12 scroll-mt-20">
  <div class="container-custom">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-4">Aurora Visibility Map</h2>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Real-time aurora probability map showing where the Northern and Southern Lights are most likely to appear.
      </p>
    </div>
    
    <div class="card p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold">Aurora Probability</h3>
        <div class="flex space-x-2">
          <button id="btn-northern" class="tab active">Northern Hemisphere</button>
          <button id="btn-southern" class="tab">Southern Hemisphere</button>
        </div>
      </div>
      
      <!-- Map Container -->
      <div id="aurora-map-container" class="h-96 rounded-lg overflow-hidden bg-dark-900 relative">
        <div id="map-loader" class="absolute inset-0 flex items-center justify-center bg-dark-800/80 backdrop-blur-sm z-10">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-400"></div>
        </div>
        <div id="aurora-map-element" class="w-full h-full"></div>
      </div>
      
      <!-- Map Legend -->
      <div class="mt-6 p-4 bg-dark-700/50 rounded-lg">
        <h4 class="font-semibold mb-3">Aurora Probability Legend</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 64, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">Very Low (0-2%)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(0, 255, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">Low (2-10%)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 255, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">Moderate (10-50%)</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 rounded mr-2" style="background: rgba(255, 0, 0, 0.7);"></div>
            <span class="text-sm text-gray-300">High (50%+)</span>
          </div>
        </div>
      </div>
      
      <!-- Map Information -->
      <div class="mt-6 p-4 bg-primary-900/30 rounded-lg">
        <h4 class="font-semibold mb-2">How to Use the Map:</h4>
        <ul class="text-sm text-gray-300 space-y-1">
          <li>• Colored regions show aurora visibility probability</li>
          <li>• Higher latitudes typically have better aurora chances</li>
          <li>• Red and orange areas indicate the best viewing locations</li>
          <li>• Green areas may have weak aurora visible to cameras</li>
          <li>• Switch between Northern and Southern Hemispheres using the tabs</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<script>
  // Import Leaflet dynamically to avoid SSR issues
  let L: any = null;
  let auroraMap: any = null;
  let currentLayer: any = null;
  let currentHemisphere = 'north';
  
  // Aurora probability color mapping
  function getProbabilityColor(probability: number): string {
    if (probability > 75) return 'rgba(255, 0, 0, 0.7)';      // Red
    if (probability > 50) return 'rgba(255, 128, 0, 0.7)';    // Orange  
    if (probability > 25) return 'rgba(255, 255, 0, 0.7)';    // Yellow
    if (probability > 10) return 'rgba(128, 255, 0, 0.7)';    // Yellow-green
    if (probability > 5)  return 'rgba(0, 255, 0, 0.7)';      // Green
    if (probability > 2)  return 'rgba(0, 128, 0, 0.7)';      // Dark green
    return probability > 0 
      ? 'rgba(0, 64, 0, 0.7)'     // Very dark green
      : 'rgba(0, 0, 0, 0)';       // Transparent
  }
  
  // Generate simulated aurora data
  function generateAuroraData(hemisphere = 'north') {
    const data = [];
    const latStart = hemisphere === 'north' ? 45 : -70;
    const latEnd = hemisphere === 'north' ? 85 : -45;
    
    for (let lat = latStart; lat <= latEnd; lat += 2) {
      for (let lng = -180; lng <= 180; lng += 5) {
        let probability = 0;
        
        if (hemisphere === 'north') {
          if (lat > 65) probability = Math.random() * 80 + 20; // High latitudes
          else if (lat > 55) probability = Math.random() * 40 + 10; // Mid latitudes
          else probability = Math.random() * 10; // Low latitudes
        } else {
          if (lat < -60) probability = Math.random() * 80 + 20; // High latitudes
          else if (lat < -50) probability = Math.random() * 40 + 10; // Mid latitudes
          else probability = Math.random() * 10; // Low latitudes
        }
        
        if (probability > 2) { // Only include locations with some probability
          data.push([lng, lat, probability]);
        }
      }
    }
    
    return data;
  }
  
  // Initialize the map
  async function initAuroraMap() {
    try {
      // Load Leaflet
      L = await import('leaflet');
      
      const mapElement = document.getElementById('aurora-map-element');
      const loader = document.getElementById('map-loader');
      
      if (!mapElement) return;
      
      // Initialize map
      auroraMap = L.map(mapElement, {
        center: [65, -100],
        zoom: 3,
        minZoom: 2,
        maxZoom: 8
      });
      
      // Add dark tile layer
      L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 19
      }).addTo(auroraMap);
      
      // Show northern hemisphere by default
      showHemisphere('north');
      
      // Hide loader
      if (loader) loader.classList.add('hidden');
      
    } catch (error) {
      console.error('Error initializing aurora map:', error);
      const loader = document.getElementById('map-loader');
      if (loader) loader.classList.add('hidden');
    }
  }
  
  // Show hemisphere data
  function showHemisphere(hemisphere: string) {
    if (!auroraMap || !L) return;
    
    // Clear existing layer
    if (currentLayer) {
      auroraMap.removeLayer(currentLayer);
    }
    
    // Generate data for hemisphere
    const auroraData = generateAuroraData(hemisphere);
    const circles: any[] = [];
    
    // Create circles for each data point
    for (const point of auroraData) {
      const [lng, lat, probability] = point;
      
      if (probability > 5) { // Only show significant probabilities
        const circle = L.circle([lat, lng], {
          color: getProbabilityColor(probability),
          fillColor: getProbabilityColor(probability),
          fillOpacity: 0.6,
          radius: 50000, // 50km radius
          weight: 1
        }).bindTooltip(`Aurora Probability: ${probability.toFixed(1)}%`);
        
        circles.push(circle);
      }
    }
    
    // Add layer to map
    currentLayer = L.layerGroup(circles);
    currentLayer.addTo(auroraMap);
    
    // Update map view
    if (hemisphere === 'north') {
      auroraMap.setView([70, -100], 3);
    } else {
      auroraMap.setView([-65, 0], 3);
    }
    
    currentHemisphere = hemisphere;
  }
  
  // Setup event listeners
  function setupEventListeners() {
    const northBtn = document.getElementById('btn-northern');
    const southBtn = document.getElementById('btn-southern');
    
    if (northBtn && southBtn) {
      northBtn.addEventListener('click', () => {
        northBtn.classList.add('active');
        southBtn.classList.remove('active');
        showHemisphere('north');
      });
      
      southBtn.addEventListener('click', () => {
        southBtn.classList.add('active');
        northBtn.classList.remove('active');
        showHemisphere('south');
      });
    }
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    initAuroraMap();
  });
</script>

<style>
  /* Tab styles */
  .tab {
    @apply px-4 py-2 rounded-md text-sm font-medium transition-all duration-200;
    @apply bg-dark-700 text-gray-300 hover:bg-dark-600 hover:text-white;
  }
  
  .tab.active {
    @apply bg-primary-600 text-white;
  }
</style> 